const express = require('express')
const cors = require('cors')
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const { Pool } = require('pg')
require('dotenv').config()

const app = express()
const port = process.env.PORT || 3002

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'bookstore',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
})

// Middleware
app.use(cors())
app.use(express.json())

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Access token required' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' })
    }
    req.user = user
    next()
  })
}

// Admin login
app.post('/api/admin/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body

    // For demo purposes, use hardcoded admin credentials
    if (email === '<EMAIL>' && password === 'admin123') {
      const token = jwt.sign(
        { id: 1, email: '<EMAIL>', role: 'admin' },
        JWT_SECRET,
        { expiresIn: '24h' }
      )

      res.json({
        token,
        user: {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin'
        }
      })
    } else {
      res.status(401).json({ message: 'Invalid credentials' })
    }
  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({ message: 'Internal server error' })
  }
})

// Admin logout
app.post('/api/admin/auth/logout', authenticateToken, (req, res) => {
  res.json({ message: 'Logged out successfully' })
})

// Get admin profile
app.get('/api/admin/auth/profile', authenticateToken, (req, res) => {
  res.json({
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin'
  })
})

// Dashboard stats
app.get('/api/admin/dashboard/stats', authenticateToken, async (req, res) => {
  try {
    const [booksResult, authorsResult, categoriesResult, reviewsResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM books'),
      pool.query('SELECT COUNT(*) FROM authors'),
      pool.query('SELECT COUNT(*) FROM categories'),
      pool.query('SELECT COUNT(*) FROM reviews')
    ])

    res.json({
      totalBooks: parseInt(booksResult.rows[0].count),
      totalAuthors: <AUTHORS>
      totalCategories: parseInt(categoriesResult.rows[0].count),
      totalReviews: parseInt(reviewsResult.rows[0].count)
    })
  } catch (error) {
    console.error('Dashboard stats error:', error)
    res.status(500).json({ message: 'Failed to fetch dashboard stats' })
  }
})

// Recent books
app.get('/api/admin/dashboard/recent-books', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT b.*, 
             COALESCE(
               json_agg(
                 json_build_object('id', a.id, 'name', a.name)
               ) FILTER (WHERE a.id IS NOT NULL), 
               '[]'
             ) as authors
      FROM books b
      LEFT JOIN book_authors ba ON b.id = ba.book_id
      LEFT JOIN authors a ON ba.author_id = a.id
      GROUP BY b.id
      ORDER BY b.created_at DESC
      LIMIT 5
    `)

    res.json(result.rows)
  } catch (error) {
    console.error('Recent books error:', error)
    res.status(500).json({ message: 'Failed to fetch recent books' })
  }
})

// Recent reviews
app.get('/api/admin/dashboard/recent-reviews', authenticateToken, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT r.*, b.title as book_title
      FROM reviews r
      JOIN books b ON r.book_id = b.id
      ORDER BY r.created_at DESC
      LIMIT 5
    `)

    res.json(result.rows)
  } catch (error) {
    console.error('Recent reviews error:', error)
    res.status(500).json({ message: 'Failed to fetch recent reviews' })
  }
})

// Books CRUD
app.get('/api/admin/books', authenticateToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1
    const limit = parseInt(req.query.limit) || 10
    const offset = (page - 1) * limit
    const search = req.query.search || ''
    const category = req.query.category || ''
    const featured = req.query.featured
    const inStock = req.query.inStock

    let whereConditions = []
    let queryParams = []
    let paramIndex = 1

    if (search) {
      whereConditions.push(`(b.title ILIKE $${paramIndex} OR b.subtitle ILIKE $${paramIndex})`)
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    if (category) {
      whereConditions.push(`EXISTS (
        SELECT 1 FROM book_categories bc 
        JOIN categories c ON bc.category_id = c.id 
        WHERE bc.book_id = b.id AND c.slug = $${paramIndex}
      )`)
      queryParams.push(category)
      paramIndex++
    }

    if (featured !== undefined) {
      whereConditions.push(`b.featured = $${paramIndex}`)
      queryParams.push(featured === 'true')
      paramIndex++
    }

    if (inStock !== undefined) {
      whereConditions.push(`b.in_stock = $${paramIndex}`)
      queryParams.push(inStock === 'true')
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT b.id) 
      FROM books b 
      ${whereClause}
    `
    const countResult = await pool.query(countQuery, queryParams)
    const total = parseInt(countResult.rows[0].count)

    // Get books with authors and categories
    const booksQuery = `
      SELECT b.*, 
             COALESCE(
               json_agg(
                 DISTINCT json_build_object('id', a.id, 'name', a.name)
               ) FILTER (WHERE a.id IS NOT NULL), 
               '[]'
             ) as authors,
             COALESCE(
               json_agg(
                 DISTINCT json_build_object('id', c.id, 'name', c.name, 'slug', c.slug)
               ) FILTER (WHERE c.id IS NOT NULL), 
               '[]'
             ) as categories
      FROM books b
      LEFT JOIN book_authors ba ON b.id = ba.book_id
      LEFT JOIN authors a ON ba.author_id = a.id
      LEFT JOIN book_categories bc ON b.id = bc.book_id
      LEFT JOIN categories c ON bc.category_id = c.id
      ${whereClause}
      GROUP BY b.id
      ORDER BY b.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    queryParams.push(limit, offset)
    const booksResult = await pool.query(booksQuery, queryParams)

    res.json({
      books: booksResult.rows,
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    })
  } catch (error) {
    console.error('Books fetch error:', error)
    res.status(500).json({ message: 'Failed to fetch books' })
  }
})

// Get single book
app.get('/api/admin/books/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    const result = await pool.query(`
      SELECT b.*, 
             COALESCE(
               json_agg(
                 DISTINCT json_build_object('id', a.id, 'name', a.name)
               ) FILTER (WHERE a.id IS NOT NULL), 
               '[]'
             ) as authors,
             COALESCE(
               json_agg(
                 DISTINCT json_build_object('id', c.id, 'name', c.name, 'slug', c.slug)
               ) FILTER (WHERE c.id IS NOT NULL), 
               '[]'
             ) as categories
      FROM books b
      LEFT JOIN book_authors ba ON b.id = ba.book_id
      LEFT JOIN authors a ON ba.author_id = a.id
      LEFT JOIN book_categories bc ON b.id = bc.book_id
      LEFT JOIN categories c ON bc.category_id = c.id
      WHERE b.id = $1
      GROUP BY b.id
    `, [id])

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Book not found' })
    }

    res.json(result.rows[0])
  } catch (error) {
    console.error('Book fetch error:', error)
    res.status(500).json({ message: 'Failed to fetch book' })
  }
})

// Create book
app.post('/api/admin/books', authenticateToken, async (req, res) => {
  const client = await pool.connect()
  
  try {
    await client.query('BEGIN')

    const {
      title, subtitle, isbn, description, price, pages, publication_date,
      language, format, cover_image_url, stock_quantity, featured, in_stock,
      author_ids, category_ids
    } = req.body

    // Insert book
    const bookResult = await client.query(`
      INSERT INTO books (
        title, subtitle, isbn, description, price, pages, publication_date,
        language, format, cover_image_url, stock_quantity, featured, in_stock
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      title, subtitle, isbn, description, price, pages, publication_date,
      language, format, cover_image_url, stock_quantity, featured, in_stock
    ])

    const book = bookResult.rows[0]

    // Insert book-author relationships
    if (author_ids && author_ids.length > 0) {
      for (const authorId of author_ids) {
        await client.query(
          'INSERT INTO book_authors (book_id, author_id) VALUES ($1, $2)',
          [book.id, authorId]
        )
      }
    }

    // Insert book-category relationships
    if (category_ids && category_ids.length > 0) {
      for (const categoryId of category_ids) {
        await client.query(
          'INSERT INTO book_categories (book_id, category_id) VALUES ($1, $2)',
          [book.id, categoryId]
        )
      }
    }

    await client.query('COMMIT')
    res.status(201).json(book)
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('Book creation error:', error)
    res.status(500).json({ message: 'Failed to create book' })
  } finally {
    client.release()
  }
})

// Start server
app.listen(port, () => {
  console.log(`Admin API server running on port ${port}`)
})

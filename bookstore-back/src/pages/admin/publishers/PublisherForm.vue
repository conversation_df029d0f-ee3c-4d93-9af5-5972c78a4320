<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">
            {{ isEdit ? 'Edit Publisher' : 'Add New Publisher' }}
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEdit ? 'Update publisher information' : 'Fill in the details to add a new publisher' }}
          </p>
        </div>
        <router-link
          :to="{ name: 'admin-publishers' }"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-2" />
          Back to Publishers
        </router-link>
      </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Publisher Information</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              Publisher Name *
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter publisher name"
            />
          </div>

          <!-- Founded Year -->
          <div>
            <label for="founded_year" class="block text-sm font-medium text-gray-700 mb-1">
              Founded Year
            </label>
            <input
              id="founded_year"
              v-model="form.founded_year"
              type="number"
              min="1000"
              :max="new Date().getFullYear()"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter founding year"
            />
          </div>

          <!-- Website -->
          <div>
            <label for="website" class="block text-sm font-medium text-gray-700 mb-1">
              Website
            </label>
            <input
              id="website"
              v-model="form.website"
              type="url"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="https://example.com"
            />
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Address -->
          <div class="lg:col-span-2">
            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <input
              id="address"
              v-model="form.address"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter street address"
            />
          </div>

          <!-- City -->
          <div>
            <label for="city" class="block text-sm font-medium text-gray-700 mb-1">
              City
            </label>
            <input
              id="city"
              v-model="form.city"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter city"
            />
          </div>

          <!-- Country -->
          <div>
            <label for="country" class="block text-sm font-medium text-gray-700 mb-1">
              Country
            </label>
            <input
              id="country"
              v-model="form.country"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter country"
            />
          </div>

          <!-- Description -->
          <div class="lg:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter publisher description"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <router-link
          :to="{ name: 'admin-publishers' }"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </router-link>
        <button
          type="submit"
          :disabled="loading"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ loading ? 'Saving...' : (isEdit ? 'Update Publisher' : 'Create Publisher') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { fetchPublisherById, createPublisher, updatePublisher } from '../../../services/adminApi'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

export default {
  name: 'PublisherForm',
  components: {
    ArrowLeftIcon
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()
    
    const loading = ref(false)
    const isEdit = computed(() => !!props.id)
    
    const form = reactive({
      name: '',
      founded_year: '',
      website: '',
      email: '',
      address: '',
      city: '',
      country: '',
      description: ''
    })

    const loadPublisher = async () => {
      if (!isEdit.value) return
      
      try {
        loading.value = true
        const publisher = await fetchPublisherById(props.id)
        
        // Populate form with publisher data
        Object.keys(form).forEach(key => {
          if (publisher[key] !== undefined) {
            form[key] = publisher[key] || ''
          }
        })
      } catch (error) {
        console.error('Failed to load publisher:', error)
        toast.error('Failed to load publisher data')
      } finally {
        loading.value = false
      }
    }

    const handleSubmit = async () => {
      try {
        loading.value = true
        
        // Validate required fields
        if (!form.name.trim()) {
          toast.error('Publisher name is required')
          return
        }
        
        const publisherData = { ...form }
        
        // Convert founded_year to number if provided
        if (publisherData.founded_year) {
          publisherData.founded_year = parseInt(publisherData.founded_year)
        }
        
        if (isEdit.value) {
          await updatePublisher(props.id, publisherData)
          toast.success('Publisher updated successfully')
        } else {
          await createPublisher(publisherData)
          toast.success('Publisher created successfully')
        }
        
        router.push({ name: 'admin-publishers' })
      } catch (error) {
        console.error('Failed to save publisher:', error)
        toast.error(error.response?.data?.message || 'Failed to save publisher')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadPublisher()
    })

    return {
      loading,
      isEdit,
      form,
      handleSubmit
    }
  }
}
</script>

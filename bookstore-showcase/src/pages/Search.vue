<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Search Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">
          {{ searchQuery ? `Search Results for "${searchQuery}"` : 'All Books' }}
        </h1>
        <p class="text-gray-600">
          {{ totalBooks }} {{ totalBooks === 1 ? 'book' : 'books' }} found
        </p>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Search Input -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              v-model="searchInput"
              @keyup.enter="performSearch"
              type="text"
              placeholder="Search books, authors, or ISBN..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
          </div>

          <!-- Sort By -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
            <select
              v-model="sortBy"
              @change="applyFilters"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
              <option value="title">Title A-Z</option>
            </select>
          </div>

          <!-- Filter Button -->
          <div class="flex items-end">
            <button
              @click="performSearch"
              class="w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors duration-200"
            >
              Search
            </button>
          </div>
        </div>
      </div>

      <!-- Results -->
      <div v-if="books.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <BookCard
          v-for="book in books"
          :key="book.id"
          :book="book"
          class="animate-fade-in"
        />
      </div>

      <!-- No Results -->
      <div v-else-if="!loading" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No books found</h3>
        <p class="text-gray-600">Try adjusting your search terms or browse our categories.</p>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="mt-12 flex justify-center">
        <nav class="flex items-center space-x-2">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-2 rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="changePage(page)"
            :class="[
              'px-3 py-2 rounded-md border',
              page === currentPage
                ? 'bg-primary-600 text-white border-primary-600'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            ]"
          >
            {{ page }}
          </button>
          
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-2 rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import BookCard from '../components/BookCard.vue'
import { searchBooks, fetchBooks } from '../services/api'

export default {
  name: 'Search',
  components: {
    BookCard
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const setLoading = inject('setLoading')
    
    const books = ref([])
    const searchInput = ref('')
    const searchQuery = ref('')
    const sortBy = ref('newest')
    const currentPage = ref(1)
    const totalBooks = ref(0)
    const totalPages = ref(0)
    const loading = ref(false)

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })

    const performSearch = async () => {
      searchQuery.value = searchInput.value
      currentPage.value = 1
      await loadBooks()
      
      // Update URL
      const query = { ...route.query }
      if (searchQuery.value) {
        query.q = searchQuery.value
      } else {
        delete query.q
      }
      router.push({ query })
    }

    const applyFilters = async () => {
      await loadBooks()
    }

    const changePage = async (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        await loadBooks()
      }
    }

    const loadBooks = async () => {
      try {
        loading.value = true
        setLoading(true)
        
        const params = {
          page: currentPage.value,
          limit: 12,
          sort: sortBy.value
        }

        let result
        if (searchQuery.value) {
          result = await searchBooks(searchQuery.value, params)
        } else {
          result = await fetchBooks(params)
        }

        books.value = result.books
        totalBooks.value = result.total
        totalPages.value = Math.ceil(result.total / params.limit)
      } catch (error) {
        console.error('Failed to load books:', error)
      } finally {
        loading.value = false
        setLoading(false)
      }
    }

    // Watch for route changes
    watch(() => route.query.q, (newQuery) => {
      searchInput.value = newQuery || ''
      searchQuery.value = newQuery || ''
      loadBooks()
    })

    onMounted(() => {
      searchInput.value = route.query.q || ''
      searchQuery.value = route.query.q || ''
      loadBooks()
    })

    return {
      books,
      searchInput,
      searchQuery,
      sortBy,
      currentPage,
      totalBooks,
      totalPages,
      loading,
      visiblePages,
      performSearch,
      applyFilters,
      changePage
    }
  }
}
</script>

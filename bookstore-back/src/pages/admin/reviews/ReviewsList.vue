<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Reviews Management
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Manage book reviews and ratings
          </p>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search reviews..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @input="debouncedSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Rating</label>
          <select
            v-model="filters.rating"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @change="applyFilters"
          >
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="filters.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @change="applyFilters"
          >
            <option value="">All Reviews</option>
            <option value="approved">Approved</option>
            <option value="pending">Pending</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Reviews list -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Reviews ({{ pagination.total }})
        </h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading reviews...</p>
      </div>
      
      <div v-else-if="reviews.length === 0" class="p-8 text-center">
        <ChatBubbleLeftRightIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No reviews found</h3>
        <p class="mt-1 text-sm text-gray-500">No reviews match your current filters.</p>
      </div>
      
      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="review in reviews"
          :key="review.id"
          class="p-6 hover:bg-gray-50 transition-colors duration-200"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <!-- Review header -->
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                    <span class="text-sm font-medium text-primary-700">
                      {{ getInitials(review.reviewer_name) }}
                    </span>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">{{ review.reviewer_name }}</p>
                    <p class="text-xs text-gray-500">{{ formatDate(review.created_at) }}</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <StarRating :rating="review.rating" size="sm" />
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getStatusClass(review.status)"
                  >
                    {{ review.status || 'pending' }}
                  </span>
                </div>
              </div>

              <!-- Book info -->
              <div class="mb-3">
                <p class="text-sm text-gray-600">
                  Review for: <span class="font-medium text-gray-900">{{ review.book_title }}</span>
                </p>
              </div>

              <!-- Review text -->
              <div class="mb-4">
                <p class="text-sm text-gray-700">{{ review.review_text }}</p>
              </div>

              <!-- Actions -->
              <div class="flex items-center space-x-4">
                <button
                  v-if="review.status !== 'approved'"
                  @click="updateReviewStatus(review.id, 'approved')"
                  class="text-sm text-green-600 hover:text-green-900 font-medium"
                >
                  Approve
                </button>
                <button
                  v-if="review.status !== 'rejected'"
                  @click="updateReviewStatus(review.id, 'rejected')"
                  class="text-sm text-red-600 hover:text-red-900 font-medium"
                >
                  Reject
                </button>
                <button
                  @click="deleteReview(review)"
                  class="text-sm text-red-600 hover:text-red-900 font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <AdminPagination
        v-if="reviews.length > 0"
        :current-page="pagination.page"
        :total-pages="pagination.totalPages"
        :total-items="pagination.total"
        @page-changed="changePage"
      />
    </div>

    <!-- Delete confirmation modal -->
    <ConfirmationModal
      v-if="reviewToDelete"
      title="Delete Review"
      :message="`Are you sure you want to delete this review by ${reviewToDelete.reviewer_name}? This action cannot be undone.`"
      @confirm="confirmDelete"
      @cancel="reviewToDelete = null"
    />
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { fetchReviews, deleteReview as deleteReviewApi, updateReviewStatus as updateReviewStatusApi } from '../../../services/adminApi'
import { ChatBubbleLeftRightIcon } from '@heroicons/vue/24/outline'
import AdminPagination from '../../../components/admin/AdminPagination.vue'
import ConfirmationModal from '../../../components/admin/ConfirmationModal.vue'
import StarRating from '../../../components/StarRating.vue'
import { useToast } from 'vue-toastification'
import { debounce } from 'lodash-es'
import { format } from 'date-fns'

export default {
  name: 'ReviewsList',
  components: {
    ChatBubbleLeftRightIcon,
    AdminPagination,
    ConfirmationModal,
    StarRating
  },
  setup() {
    const toast = useToast()
    const loading = ref(false)
    const reviews = ref([])
    const reviewToDelete = ref(null)
    
    const filters = reactive({
      search: '',
      rating: '',
      status: ''
    })
    
    const pagination = ref({
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    })

    const getInitials = (name) => {
      return name.split(' ').map(n => n[0]).join('').toUpperCase()
    }

    const formatDate = (date) => {
      return format(new Date(date), 'MMM dd, yyyy HH:mm')
    }

    const getStatusClass = (status) => {
      const classes = {
        approved: 'bg-green-100 text-green-800',
        pending: 'bg-yellow-100 text-yellow-800',
        rejected: 'bg-red-100 text-red-800'
      }
      return classes[status] || classes.pending
    }

    const loadReviews = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.value.page,
          limit: pagination.value.limit
        }
        
        if (filters.search) params.search = filters.search
        if (filters.rating) params.rating = parseInt(filters.rating)
        if (filters.status) params.status = filters.status
        
        const response = await fetchReviews(params)
        
        reviews.value = response.reviews || response
        pagination.value = {
          page: response.page || 1,
          limit: response.limit || 10,
          total: response.total || reviews.value.length,
          totalPages: response.totalPages || Math.ceil((response.total || reviews.value.length) / (response.limit || 10))
        }
      } catch (error) {
        console.error('Failed to load reviews:', error)
        toast.error('Failed to load reviews')
      } finally {
        loading.value = false
      }
    }

    const applyFilters = () => {
      pagination.value.page = 1
      loadReviews()
    }

    const debouncedSearch = debounce(() => {
      applyFilters()
    }, 500)

    const changePage = (page) => {
      pagination.value.page = page
      loadReviews()
    }

    const updateReviewStatus = async (reviewId, status) => {
      try {
        await updateReviewStatusApi(reviewId, status)
        toast.success(`Review ${status} successfully`)
        loadReviews()
      } catch (error) {
        toast.error(`Failed to ${status} review`)
      }
    }

    const deleteReview = (review) => {
      reviewToDelete.value = review
    }

    const confirmDelete = async () => {
      try {
        await deleteReviewApi(reviewToDelete.value.id)
        toast.success('Review deleted successfully')
        reviewToDelete.value = null
        loadReviews()
      } catch (error) {
        toast.error('Failed to delete review')
      }
    }

    onMounted(() => {
      loadReviews()
    })

    return {
      loading,
      reviews,
      reviewToDelete,
      filters,
      pagination,
      getInitials,
      formatDate,
      getStatusClass,
      applyFilters,
      debouncedSearch,
      changePage,
      updateReviewStatus,
      deleteReview,
      confirmDelete
    }
  }
}
</script>

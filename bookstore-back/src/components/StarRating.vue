<template>
  <div class="flex items-center">
    <div class="flex items-center">
      <StarIcon
        v-for="star in 5"
        :key="star"
        class="flex-shrink-0"
        :class="[
          star <= rating ? 'text-yellow-400' : 'text-gray-300',
          sizeClasses
        ]"
      />
    </div>
    <span v-if="showRating" class="ml-2 text-sm text-gray-600">
      {{ rating.toFixed(1) }}
    </span>
  </div>
</template>

<script>
import { computed } from 'vue'
import { StarIcon } from '@heroicons/vue/24/solid'

export default {
  name: 'StarRating',
  components: {
    StarIcon
  },
  props: {
    rating: {
      type: Number,
      required: true
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => ['sm', 'md', 'lg'].includes(value)
    },
    showRating: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const sizeClasses = computed(() => {
      const sizes = {
        sm: 'h-4 w-4',
        md: 'h-5 w-5',
        lg: 'h-6 w-6'
      }
      return sizes[props.size]
    })

    return {
      sizeClasses
    }
  }
}
</script>

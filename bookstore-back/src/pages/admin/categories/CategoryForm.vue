<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">
            {{ isEdit ? 'Edit Category' : 'Add New Category' }}
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEdit ? 'Update category information' : 'Fill in the details to add a new category' }}
          </p>
        </div>
        <router-link
          :to="{ name: 'admin-categories' }"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-2" />
          Back to Categories
        </router-link>
      </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Category Information</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              Category Name *
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter category name"
              @input="generateSlug"
            />
          </div>

          <!-- Slug -->
          <div>
            <label for="slug" class="block text-sm font-medium text-gray-700 mb-1">
              Slug *
            </label>
            <input
              id="slug"
              v-model="form.slug"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="category-slug"
            />
            <p class="mt-1 text-sm text-gray-500">
              URL-friendly version of the name. Will be auto-generated if left empty.
            </p>
          </div>

          <!-- Description -->
          <div class="lg:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter category description"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <router-link
          :to="{ name: 'admin-categories' }"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </router-link>
        <button
          type="submit"
          :disabled="loading"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ loading ? 'Saving...' : (isEdit ? 'Update Category' : 'Create Category') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { fetchCategoryById, createCategory, updateCategory } from '../../../services/adminApi'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

export default {
  name: 'CategoryForm',
  components: {
    ArrowLeftIcon
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()
    
    const loading = ref(false)
    const isEdit = computed(() => !!props.id)
    
    const form = reactive({
      name: '',
      slug: '',
      description: ''
    })

    const generateSlug = () => {
      if (!isEdit.value && form.name && !form.slug) {
        form.slug = form.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '')
      }
    }

    const loadCategory = async () => {
      if (!isEdit.value) return
      
      try {
        loading.value = true
        const category = await fetchCategoryById(props.id)
        
        // Populate form with category data
        Object.keys(form).forEach(key => {
          if (category[key] !== undefined) {
            form[key] = category[key] || ''
          }
        })
      } catch (error) {
        console.error('Failed to load category:', error)
        toast.error('Failed to load category data')
      } finally {
        loading.value = false
      }
    }

    const handleSubmit = async () => {
      try {
        loading.value = true
        
        // Validate required fields
        if (!form.name.trim()) {
          toast.error('Category name is required')
          return
        }
        
        if (!form.slug.trim()) {
          generateSlug()
        }
        
        const categoryData = { ...form }
        
        if (isEdit.value) {
          await updateCategory(props.id, categoryData)
          toast.success('Category updated successfully')
        } else {
          await createCategory(categoryData)
          toast.success('Category created successfully')
        }
        
        router.push({ name: 'admin-categories' })
      } catch (error) {
        console.error('Failed to save category:', error)
        toast.error(error.response?.data?.message || 'Failed to save category')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadCategory()
    })

    return {
      loading,
      isEdit,
      form,
      generateSlug,
      handleSubmit
    }
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Books Management
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Manage your book catalog
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <router-link
            :to="{ name: 'admin-books-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Book
          </router-link>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input
            v-model="filters.search"
            type="text"
            placeholder="Search books..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @input="debouncedSearch"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <select
            v-model="filters.category"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @change="applyFilters"
          >
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category.id" :value="category.slug">
              {{ category.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Featured</label>
          <select
            v-model="filters.featured"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @change="applyFilters"
          >
            <option value="">All Books</option>
            <option value="true">Featured Only</option>
            <option value="false">Non-Featured</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="filters.inStock"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            @change="applyFilters"
          >
            <option value="">All Books</option>
            <option value="true">In Stock</option>
            <option value="false">Out of Stock</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Books table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Books ({{ booksStore.pagination.total }})
        </h3>
      </div>
      
      <div v-if="booksStore.loading" class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading books...</p>
      </div>
      
      <div v-else-if="booksStore.books.length === 0" class="p-8 text-center">
        <BookOpenIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No books found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding a new book.</p>
        <div class="mt-6">
          <router-link
            :to="{ name: 'admin-books-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Book
          </router-link>
        </div>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Book
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Author(s)
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="book in booksStore.books" :key="book.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img
                    :src="book.cover_image_url || '/placeholder-book.jpg'"
                    :alt="book.title"
                    class="h-16 w-12 object-cover rounded"
                  />
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 line-clamp-2">{{ book.title }}</div>
                    <div v-if="book.subtitle" class="text-sm text-gray-500 line-clamp-1">{{ book.subtitle }}</div>
                    <div class="text-xs text-gray-400">ISBN: {{ book.isbn || 'N/A' }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatAuthors(book.authors) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  v-for="category in book.categories?.slice(0, 2)"
                  :key="category.id"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 mr-1"
                >
                  {{ category.name }}
                </span>
                <span v-if="book.categories?.length > 2" class="text-xs text-gray-500">
                  +{{ book.categories.length - 2 }} more
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${{ book.price }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex flex-col space-y-1">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="book.in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                  >
                    {{ book.in_stock ? 'In Stock' : 'Out of Stock' }}
                  </span>
                  <span
                    v-if="book.featured"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                  >
                    Featured
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <router-link
                    :to="{ name: 'admin-books-edit', params: { id: book.id } }"
                    class="text-primary-600 hover:text-primary-900"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </router-link>
                  <button
                    @click="deleteBook(book)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <AdminPagination
        v-if="booksStore.books.length > 0"
        :current-page="booksStore.pagination.page"
        :total-pages="booksStore.pagination.totalPages"
        :total-items="booksStore.pagination.total"
        @page-changed="changePage"
      />
    </div>

    <!-- Delete confirmation modal -->
    <ConfirmationModal
      v-if="bookToDelete"
      title="Delete Book"
      :message="`Are you sure you want to delete '${bookToDelete.title}'? This action cannot be undone.`"
      @confirm="confirmDelete"
      @cancel="bookToDelete = null"
    />
  </div>
</template>

<script>
import { ref, onMounted, reactive } from 'vue'
import { useBooksStore } from '../../../stores/books'
import { fetchCategories } from '../../../services/adminApi'
import { PlusIcon, BookOpenIcon, PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'
import AdminPagination from '../../../components/admin/AdminPagination.vue'
import ConfirmationModal from '../../../components/admin/ConfirmationModal.vue'
import { useToast } from 'vue-toastification'
import { debounce } from 'lodash-es'

export default {
  name: 'BooksList',
  components: {
    PlusIcon,
    BookOpenIcon,
    PencilIcon,
    TrashIcon,
    AdminPagination,
    ConfirmationModal
  },
  setup() {
    const booksStore = useBooksStore()
    const toast = useToast()
    const categories = ref([])
    const bookToDelete = ref(null)

    const filters = reactive({
      search: '',
      category: '',
      featured: '',
      inStock: ''
    })

    const formatAuthors = (authors) => {
      if (!authors || authors.length === 0) return 'Unknown Author'
      return authors.map(author => author.name).join(', ')
    }

    const applyFilters = () => {
      booksStore.setPage(1)
      loadBooks()
    }

    const debouncedSearch = debounce(() => {
      applyFilters()
    }, 500)

    const loadBooks = async () => {
      try {
        const params = {}
        if (filters.search) params.search = filters.search
        if (filters.category) params.category = filters.category
        if (filters.featured) params.featured = filters.featured === 'true'
        if (filters.inStock) params.inStock = filters.inStock === 'true'

        await booksStore.fetchBooks(params)
      } catch (error) {
        toast.error('Failed to load books')
      }
    }

    const loadCategories = async () => {
      try {
        categories.value = await fetchCategories()
      } catch (error) {
        console.error('Failed to load categories:', error)
      }
    }

    const changePage = (page) => {
      booksStore.setPage(page)
      loadBooks()
    }

    const deleteBook = (book) => {
      bookToDelete.value = book
    }

    const confirmDelete = async () => {
      try {
        await booksStore.removeBook(bookToDelete.value.id)
        toast.success('Book deleted successfully')
        bookToDelete.value = null
      } catch (error) {
        toast.error('Failed to delete book')
      }
    }

    onMounted(() => {
      loadBooks()
      loadCategories()
    })

    return {
      booksStore,
      categories,
      filters,
      bookToDelete,
      formatAuthors,
      applyFilters,
      debouncedSearch,
      changePage,
      deleteBook,
      confirmDelete
    }
  }
}
</script>

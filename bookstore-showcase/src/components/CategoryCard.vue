<template>
  <router-link
    :to="`/category/${category.slug}`"
    class="block bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 p-6 text-center animate-fade-in"
  >
    <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
      <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
    </div>
    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ category.name }}</h3>
    <p class="text-sm text-gray-600">{{ category.description }}</p>
  </router-link>
</template>

<script>
export default {
  name: 'CategoryCard',
  props: {
    category: {
      type: Object,
      required: true
    }
  }
}
</script>

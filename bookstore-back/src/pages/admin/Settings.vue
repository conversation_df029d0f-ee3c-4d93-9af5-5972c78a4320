<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Settings</h2>
          <p class="mt-1 text-sm text-gray-500">
            Manage your admin account and application settings
          </p>
        </div>
      </div>
    </div>

    <!-- Profile Settings -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">Profile Settings</h3>
      
      <form @submit.prevent="updateProfile" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              Full Name
            </label>
            <input
              id="name"
              v-model="profileForm.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your full name"
            />
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              id="email"
              v-model="profileForm.email"
              type="email"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter your email"
            />
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="profileLoading"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ profileLoading ? 'Updating...' : 'Update Profile' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Password Change -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">Change Password</h3>
      
      <form @submit.prevent="changePassword" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Current Password -->
          <div>
            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-1">
              Current Password
            </label>
            <div class="relative">
              <input
                id="current_password"
                v-model="passwordForm.current_password"
                :type="showCurrentPassword ? 'text' : 'password'"
                required
                class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter current password"
              />
              <button
                type="button"
                @click="showCurrentPassword = !showCurrentPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <EyeIcon v-if="!showCurrentPassword" class="h-5 w-5 text-gray-400" />
                <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
              </button>
            </div>
          </div>

          <!-- New Password -->
          <div>
            <label for="new_password" class="block text-sm font-medium text-gray-700 mb-1">
              New Password
            </label>
            <div class="relative">
              <input
                id="new_password"
                v-model="passwordForm.new_password"
                :type="showNewPassword ? 'text' : 'password'"
                required
                minlength="6"
                class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter new password"
              />
              <button
                type="button"
                @click="showNewPassword = !showNewPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <EyeIcon v-if="!showNewPassword" class="h-5 w-5 text-gray-400" />
                <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
              </button>
            </div>
          </div>

          <!-- Confirm Password -->
          <div class="lg:col-span-2">
            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">
              Confirm New Password
            </label>
            <div class="relative max-w-md">
              <input
                id="confirm_password"
                v-model="passwordForm.confirm_password"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="Confirm new password"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <EyeIcon v-if="!showConfirmPassword" class="h-5 w-5 text-gray-400" />
                <EyeSlashIcon v-else class="h-5 w-5 text-gray-400" />
              </button>
            </div>
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="passwordLoading"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ passwordLoading ? 'Changing...' : 'Change Password' }}
          </button>
        </div>
      </form>
    </div>

    <!-- Application Settings -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-6">Application Settings</h3>
      
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-sm font-medium text-gray-900">Email Notifications</h4>
            <p class="text-sm text-gray-500">Receive email notifications for new reviews and orders</p>
          </div>
          <button
            @click="toggleNotifications"
            :class="[
              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
              notificationsEnabled ? 'bg-primary-600' : 'bg-gray-200'
            ]"
          >
            <span
              :class="[
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                notificationsEnabled ? 'translate-x-5' : 'translate-x-0'
              ]"
            />
          </button>
        </div>

        <div class="flex items-center justify-between">
          <div>
            <h4 class="text-sm font-medium text-gray-900">Auto-approve Reviews</h4>
            <p class="text-sm text-gray-500">Automatically approve new book reviews</p>
          </div>
          <button
            @click="toggleAutoApprove"
            :class="[
              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
              autoApproveEnabled ? 'bg-primary-600' : 'bg-gray-200'
            ]"
          >
            <span
              :class="[
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                autoApproveEnabled ? 'translate-x-5' : 'translate-x-0'
              ]"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

export default {
  name: 'AdminSettings',
  components: {
    EyeIcon,
    EyeSlashIcon
  },
  setup() {
    const authStore = useAuthStore()
    const toast = useToast()

    const profileLoading = ref(false)
    const passwordLoading = ref(false)
    const showCurrentPassword = ref(false)
    const showNewPassword = ref(false)
    const showConfirmPassword = ref(false)
    const notificationsEnabled = ref(true)
    const autoApproveEnabled = ref(false)

    const profileForm = reactive({
      name: '',
      email: ''
    })

    const passwordForm = reactive({
      current_password: '',
      new_password: '',
      confirm_password: ''
    })

    const loadProfile = () => {
      if (authStore.user) {
        profileForm.name = authStore.user.name || ''
        profileForm.email = authStore.user.email || ''
      }
    }

    const updateProfile = async () => {
      try {
        profileLoading.value = true

        // Here you would call an API to update the profile
        // await updateAdminProfile(profileForm)

        toast.success('Profile updated successfully')
      } catch (error) {
        toast.error('Failed to update profile')
      } finally {
        profileLoading.value = false
      }
    }

    const changePassword = async () => {
      try {
        passwordLoading.value = true

        // Validate passwords match
        if (passwordForm.new_password !== passwordForm.confirm_password) {
          toast.error('New passwords do not match')
          return
        }

        // Here you would call an API to change the password
        // await changeAdminPassword(passwordForm)

        // Reset form
        Object.keys(passwordForm).forEach(key => {
          passwordForm[key] = ''
        })

        toast.success('Password changed successfully')
      } catch (error) {
        toast.error('Failed to change password')
      } finally {
        passwordLoading.value = false
      }
    }

    const toggleNotifications = () => {
      notificationsEnabled.value = !notificationsEnabled.value
      toast.success(`Email notifications ${notificationsEnabled.value ? 'enabled' : 'disabled'}`)
    }

    const toggleAutoApprove = () => {
      autoApproveEnabled.value = !autoApproveEnabled.value
      toast.success(`Auto-approve reviews ${autoApproveEnabled.value ? 'enabled' : 'disabled'}`)
    }

    onMounted(() => {
      loadProfile()
    })

    return {
      profileLoading,
      passwordLoading,
      showCurrentPassword,
      showNewPassword,
      showConfirmPassword,
      notificationsEnabled,
      autoApproveEnabled,
      profileForm,
      passwordForm,
      updateProfile,
      changePassword,
      toggleNotifications,
      toggleAutoApprove
    }
  }
}
</script>

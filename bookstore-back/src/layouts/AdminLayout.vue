<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out"
         :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }"
         @click.self="sidebarOpen = false">
      
      <div class="flex items-center justify-center h-16 px-4 bg-primary-600">
        <h1 class="text-xl font-bold text-white">BookStore Admin</h1>
      </div>
      
      <nav class="mt-5 px-2">
        <div class="space-y-1">
          <router-link
            v-for="item in navigation"
            :key="item.name"
            :to="item.to"
            class="group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200"
            :class="[
              $route.name === item.to.name || $route.path.startsWith(item.basePath)
                ? 'bg-primary-100 text-primary-900'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            ]"
          >
            <component
              :is="item.icon"
              class="mr-3 h-6 w-6 flex-shrink-0"
              :class="[
                $route.name === item.to.name || $route.path.startsWith(item.basePath)
                  ? 'text-primary-500'
                  : 'text-gray-400 group-hover:text-gray-500'
              ]"
            />
            {{ item.name }}
          </router-link>
        </div>
      </nav>
    </div>

    <!-- Mobile sidebar overlay -->
    <div v-show="sidebarOpen" class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
         @click="sidebarOpen = false"></div>

    <!-- Main content -->
    <div class="lg:pl-64 flex flex-col flex-1">
      <!-- Top navigation -->
      <div class="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-white shadow">
        <button
          @click="sidebarOpen = !sidebarOpen"
          class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden"
        >
          <Bars3Icon class="h-6 w-6" />
        </button>

        <div class="flex-1 px-4 flex justify-between items-center">
          <!-- Page title -->
          <div class="flex-1">
            <h2 class="text-lg font-medium text-gray-900">{{ pageTitle }}</h2>
          </div>

          <!-- User menu -->
          <div class="ml-4 flex items-center space-x-4">
            <!-- Notifications -->
            <button class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              <BellIcon class="h-6 w-6" />
            </button>

            <!-- Profile dropdown -->
            <div class="relative">
              <button
                @click="userMenuOpen = !userMenuOpen"
                class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    {{ userInitials }}
                  </span>
                </div>
                <span class="ml-3 text-gray-700 text-sm font-medium">{{ authStore.user?.name }}</span>
                <ChevronDownIcon class="ml-2 h-4 w-4 text-gray-400" />
              </button>

              <!-- Dropdown menu -->
              <div v-show="userMenuOpen" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <router-link
                    to="/admin/settings"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    @click="userMenuOpen = false"
                  >
                    Settings
                  </router-link>
                  <button
                    @click="handleLogout"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Sign out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Page content -->
      <main class="flex-1">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <router-view />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import {
  Bars3Icon,
  BellIcon,
  ChevronDownIcon,
  HomeIcon,
  BookOpenIcon,
  UserGroupIcon,
  TagIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'AdminLayout',
  components: {
    Bars3Icon,
    BellIcon,
    ChevronDownIcon
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const sidebarOpen = ref(false)
    const userMenuOpen = ref(false)

    const navigation = [
      {
        name: 'Dashboard',
        to: { name: 'admin-dashboard' },
        icon: HomeIcon,
        basePath: '/admin'
      },
      {
        name: 'Books',
        to: { name: 'admin-books' },
        icon: BookOpenIcon,
        basePath: '/admin/books'
      },
      {
        name: 'Authors',
        to: { name: 'admin-authors' },
        icon: UserGroupIcon,
        basePath: '/admin/authors'
      },
      {
        name: 'Categories',
        to: { name: 'admin-categories' },
        icon: TagIcon,
        basePath: '/admin/categories'
      },
      {
        name: 'Publishers',
        to: { name: 'admin-publishers' },
        icon: BuildingOfficeIcon,
        basePath: '/admin/publishers'
      },
      {
        name: 'Reviews',
        to: { name: 'admin-reviews' },
        icon: ChatBubbleLeftRightIcon,
        basePath: '/admin/reviews'
      },
      {
        name: 'Settings',
        to: { name: 'admin-settings' },
        icon: Cog6ToothIcon,
        basePath: '/admin/settings'
      }
    ]

    const pageTitle = computed(() => {
      const currentRoute = router.currentRoute.value
      const navItem = navigation.find(item =>
        currentRoute.name === item.to.name ||
        currentRoute.path.startsWith(item.basePath)
      )
      return navItem?.name || 'Admin Panel'
    })

    const userInitials = computed(() => {
      const name = authStore.user?.name || 'Admin'
      return name.split(' ').map(n => n[0]).join('').toUpperCase()
    })

    const handleLogout = async () => {
      await authStore.logout()
      router.push({ name: 'admin-login' })
    }

    onMounted(() => {
      if (!authStore.user) {
        authStore.fetchProfile()
      }
    })

    return {
      sidebarOpen,
      userMenuOpen,
      navigation,
      pageTitle,
      userInitials,
      authStore,
      handleLogout
    }
  }
}
</script>

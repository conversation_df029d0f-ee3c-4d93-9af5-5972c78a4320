<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Categories Management
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Manage book categories
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <router-link
            :to="{ name: 'admin-categories-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Category
          </router-link>
        </div>
      </div>
    </div>

    <!-- Search -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="max-w-md">
        <label class="block text-sm font-medium text-gray-700 mb-1">Search Categories</label>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search by name..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          @input="debouncedSearch"
        />
      </div>
    </div>

    <!-- Categories grid -->
    <div v-if="loading" class="bg-white shadow rounded-lg p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      <p class="mt-2 text-sm text-gray-500">Loading categories...</p>
    </div>
    
    <div v-else-if="categories.length === 0" class="bg-white shadow rounded-lg p-8 text-center">
      <TagIcon class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-2 text-sm font-medium text-gray-900">No categories found</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by adding a new category.</p>
      <div class="mt-6">
        <router-link
          :to="{ name: 'admin-categories-create' }"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <PlusIcon class="h-4 w-4 mr-2" />
          Add Category
        </router-link>
      </div>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="category in categories"
        :key="category.id"
        class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow duration-200"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="h-10 w-10 rounded-lg bg-primary-100 flex items-center justify-center">
              <TagIcon class="h-6 w-6 text-primary-600" />
            </div>
            <div class="ml-3">
              <h3 class="text-lg font-medium text-gray-900">{{ category.name }}</h3>
              <p class="text-sm text-gray-500">{{ category.slug }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <router-link
              :to="{ name: 'admin-categories-edit', params: { id: category.id } }"
              class="text-primary-600 hover:text-primary-900"
            >
              <PencilIcon class="h-4 w-4" />
            </router-link>
            <button
              @click="deleteCategory(category)"
              class="text-red-600 hover:text-red-900"
            >
              <TrashIcon class="h-4 w-4" />
            </button>
          </div>
        </div>
        
        <div v-if="category.description" class="mb-4">
          <p class="text-sm text-gray-600 line-clamp-3">{{ category.description }}</p>
        </div>
        
        <div class="flex items-center justify-between text-sm text-gray-500">
          <span>{{ category.books_count || 0 }} books</span>
          <span>{{ formatDate(category.created_at) }}</span>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <AdminPagination
      v-if="categories.length > 0"
      :current-page="pagination.page"
      :total-pages="pagination.totalPages"
      :total-items="pagination.total"
      @page-changed="changePage"
    />

    <!-- Delete confirmation modal -->
    <ConfirmationModal
      v-if="categoryToDelete"
      title="Delete Category"
      :message="`Are you sure you want to delete '${categoryToDelete.name}'? This action cannot be undone.`"
      @confirm="confirmDelete"
      @cancel="categoryToDelete = null"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { fetchCategories, deleteCategory as deleteCategoryApi } from '../../../services/adminApi'
import { PlusIcon, TagIcon, PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'
import AdminPagination from '../../../components/admin/AdminPagination.vue'
import ConfirmationModal from '../../../components/admin/ConfirmationModal.vue'
import { useToast } from 'vue-toastification'
import { debounce } from 'lodash-es'
import { format } from 'date-fns'

export default {
  name: 'CategoriesList',
  components: {
    PlusIcon,
    TagIcon,
    PencilIcon,
    TrashIcon,
    AdminPagination,
    ConfirmationModal
  },
  setup() {
    const toast = useToast()
    const loading = ref(false)
    const categories = ref([])
    const categoryToDelete = ref(null)
    const searchQuery = ref('')
    
    const pagination = ref({
      page: 1,
      limit: 12,
      total: 0,
      totalPages: 0
    })

    const formatDate = (date) => {
      return format(new Date(date), 'MMM dd, yyyy')
    }

    const loadCategories = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.value.page,
          limit: pagination.value.limit
        }
        
        if (searchQuery.value) {
          params.search = searchQuery.value
        }
        
        const response = await fetchCategories(params)
        
        categories.value = response.categories || response
        pagination.value = {
          page: response.page || 1,
          limit: response.limit || 12,
          total: response.total || categories.value.length,
          totalPages: response.totalPages || Math.ceil((response.total || categories.value.length) / (response.limit || 12))
        }
      } catch (error) {
        console.error('Failed to load categories:', error)
        toast.error('Failed to load categories')
      } finally {
        loading.value = false
      }
    }

    const debouncedSearch = debounce(() => {
      pagination.value.page = 1
      loadCategories()
    }, 500)

    const changePage = (page) => {
      pagination.value.page = page
      loadCategories()
    }

    const deleteCategory = (category) => {
      categoryToDelete.value = category
    }

    const confirmDelete = async () => {
      try {
        await deleteCategoryApi(categoryToDelete.value.id)
        toast.success('Category deleted successfully')
        categoryToDelete.value = null
        loadCategories()
      } catch (error) {
        toast.error('Failed to delete category')
      }
    }

    onMounted(() => {
      loadCategories()
    })

    return {
      loading,
      categories,
      categoryToDelete,
      searchQuery,
      pagination,
      formatDate,
      debouncedSearch,
      changePage,
      deleteCategory,
      confirmDelete
    }
  }
}
</script>

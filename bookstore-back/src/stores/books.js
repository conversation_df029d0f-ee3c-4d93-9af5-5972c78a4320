import { defineStore } from 'pinia'
import { ref } from 'vue'
import { 
  fetchAdminBooks, 
  createBook, 
  updateBook, 
  deleteBook,
  fetchBookById 
} from '../services/adminApi'

export const useBooksStore = defineStore('books', () => {
  const books = ref([])
  const currentBook = ref(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  const fetchBooks = async (params = {}) => {
    try {
      loading.value = true
      const response = await fetchAdminBooks({
        page: pagination.value.page,
        limit: pagination.value.limit,
        ...params
      })
      
      books.value = response.books
      pagination.value = {
        page: response.page,
        limit: response.limit,
        total: response.total,
        totalPages: response.totalPages
      }
    } catch (error) {
      console.error('Failed to fetch books:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchBook = async (id) => {
    try {
      loading.value = true
      currentBook.value = await fetchBookById(id)
      return currentBook.value
    } catch (error) {
      console.error('Failed to fetch book:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const saveBook = async (bookData) => {
    try {
      loading.value = true
      let result
      
      if (bookData.id) {
        result = await updateBook(bookData.id, bookData)
        // Update in local state
        const index = books.value.findIndex(book => book.id === bookData.id)
        if (index !== -1) {
          books.value[index] = result
        }
      } else {
        result = await createBook(bookData)
        books.value.unshift(result)
      }
      
      return result
    } catch (error) {
      console.error('Failed to save book:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const removeBook = async (id) => {
    try {
      await deleteBook(id)
      books.value = books.value.filter(book => book.id !== id)
    } catch (error) {
      console.error('Failed to delete book:', error)
      throw error
    }
  }

  const setPage = (page) => {
    pagination.value.page = page
  }

  return {
    books,
    currentBook,
    loading,
    pagination,
    fetchBooks,
    fetchBook,
    saveBook,
    removeBook,
    setPage
  }
})

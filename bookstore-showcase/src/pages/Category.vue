<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Category Header -->
      <div v-if="category" class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">
          {{ category.name }}
        </h1>
        <p class="text-lg text-gray-600 mb-4">
          {{ category.description }}
        </p>
        <p class="text-gray-600">
          {{ totalBooks }} {{ totalBooks === 1 ? 'book' : 'books' }} in this category
        </p>
      </div>

      <!-- Sort Options -->
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700">Sort by:</label>
            <select
              v-model="sortBy"
              @change="applySort"
              class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Highest Rated</option>
              <option value="title">Title A-Z</option>
            </select>
          </div>

          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">View:</span>
            <button
              @click="viewMode = 'grid'"
              :class="[
                'p-2 rounded-md',
                viewMode === 'grid' ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'
              ]"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
              </svg>
            </button>
            <button
              @click="viewMode = 'list'"
              :class="[
                'p-2 rounded-md',
                viewMode === 'list' ? 'bg-primary-100 text-primary-600' : 'text-gray-400 hover:text-gray-600'
              ]"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Books Grid -->
      <div v-if="books.length > 0 && viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <BookCard
          v-for="book in books"
          :key="book.id"
          :book="book"
          class="animate-fade-in"
        />
      </div>

      <!-- Books List -->
      <div v-else-if="books.length > 0 && viewMode === 'list'" class="space-y-6">
        <div
          v-for="book in books"
          :key="book.id"
          class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 animate-fade-in"
        >
          <div class="md:flex">
            <div class="md:w-48">
              <img
                :src="book.cover_image_url || '/placeholder-book.jpg'"
                :alt="book.title"
                class="w-full h-48 md:h-full object-cover"
              >
            </div>
            <div class="p-6 flex-1">
              <div class="flex justify-between items-start mb-4">
                <div>
                  <h3 class="text-xl font-semibold text-gray-900 mb-2">
                    <router-link :to="`/book/${book.id}`" class="hover:text-primary-600 transition-colors duration-200">
                      {{ book.title }}
                    </router-link>
                  </h3>
                  <p class="text-gray-600 mb-2">by {{ formatAuthors(book.authors) }}</p>
                  <div class="flex items-center mb-2">
                    <StarRating :rating="book.rating" />
                    <span class="ml-2 text-sm text-gray-600">
                      ({{ book.review_count }} reviews)
                    </span>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-2xl font-bold text-primary-600">${{ book.price }}</p>
                  <p v-if="book.featured" class="text-xs text-yellow-600 font-medium">Featured</p>
                </div>
              </div>
              <p class="text-gray-700 mb-4 line-clamp-3">{{ book.description }}</p>
              <div class="flex justify-between items-center">
                <router-link
                  :to="`/book/${book.id}`"
                  class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200"
                >
                  View Details
                </router-link>
                <span class="text-sm text-gray-500">{{ book.pages }} pages</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Results -->
      <div v-else-if="!loading" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No books found in this category</h3>
        <p class="text-gray-600">Check back later for new additions.</p>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="mt-12 flex justify-center">
        <nav class="flex items-center space-x-2">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-2 rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="changePage(page)"
            :class="[
              'px-3 py-2 rounded-md border',
              page === currentPage
                ? 'bg-primary-600 text-white border-primary-600'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            ]"
          >
            {{ page }}
          </button>
          
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-2 rounded-md bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch, inject } from 'vue'
import { useRoute } from 'vue-router'
import BookCard from '../components/BookCard.vue'
import StarRating from '../components/StarRating.vue'
import { fetchBooksByCategory } from '../services/api'

export default {
  name: 'Category',
  components: {
    BookCard,
    StarRating
  },
  setup() {
    const route = useRoute()
    const setLoading = inject('setLoading')
    
    const books = ref([])
    const category = ref(null)
    const sortBy = ref('newest')
    const viewMode = ref('grid')
    const currentPage = ref(1)
    const totalBooks = ref(0)
    const totalPages = ref(0)
    const loading = ref(false)

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
      
      return pages
    })

    const formatAuthors = (authors) => {
      if (!authors || authors.length === 0) return 'Unknown Author'
      return authors.map(author => author.name).join(', ')
    }

    const applySort = async () => {
      currentPage.value = 1
      await loadBooks()
    }

    const changePage = async (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
        await loadBooks()
      }
    }

    const loadBooks = async () => {
      try {
        loading.value = true
        setLoading(true)
        
        const params = {
          page: currentPage.value,
          limit: 12,
          sort: sortBy.value
        }

        const result = await fetchBooksByCategory(route.params.slug, params)
        
        books.value = result.books
        category.value = result.category
        totalBooks.value = result.total
        totalPages.value = Math.ceil(result.total / params.limit)
      } catch (error) {
        console.error('Failed to load category books:', error)
      } finally {
        loading.value = false
        setLoading(false)
      }
    }

    // Watch for route changes
    watch(() => route.params.slug, () => {
      currentPage.value = 1
      loadBooks()
    })

    onMounted(() => {
      loadBooks()
    })

    return {
      books,
      category,
      sortBy,
      viewMode,
      currentPage,
      totalBooks,
      totalPages,
      loading,
      visiblePages,
      formatAuthors,
      applySort,
      changePage
    }
  }
}
</script>

import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import { createRouter, createWebHistory } from 'vue-router'
import Home from './pages/Home.vue'
import BookDetail from './pages/BookDetail.vue'
import Category from './pages/Category.vue'
import Search from './pages/Search.vue'

const routes = [
  { path: '/', component: Home, name: 'home' },
  { path: '/book/:id', component: BookDetail, name: 'book-detail' },
  { path: '/category/:slug', component: Category, name: 'category' },
  { path: '/search', component: Search, name: 'search' }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

createApp(App).use(router).mount('#app')

{"name": "bookstore-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/index.js"}, "dependencies": {"vue": "^3.4.0", "@vueuse/core": "^10.7.0", "axios": "^1.6.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "vue-toastification": "^2.0.0-rc.5", "@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "date-fns": "^2.30.0", "lodash-es": "^4.17.21", "pg": "^8.11.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "express": "^4.18.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2"}}
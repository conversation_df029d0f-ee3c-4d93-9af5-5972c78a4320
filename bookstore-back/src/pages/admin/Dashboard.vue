<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Welcome back, {{ authStore.user?.name }}! Here's what's happening with your bookstore.
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <button
            @click="refreshData"
            class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowPathIcon class="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>
    </div>

    <!-- Stats cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div
        v-for="stat in stats"
        :key="stat.name"
        class="bg-white overflow-hidden shadow rounded-lg transform hover:scale-105 transition-transform duration-200"
      >
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <component
                :is="stat.icon"
                class="h-6 w-6"
                :class="stat.iconColor"
              />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  {{ stat.name }}
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">
                    {{ stat.value }}
                  </div>
                  <div
                    v-if="stat.change"
                    class="ml-2 flex items-baseline text-sm font-semibold"
                    :class="stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'"
                  >
                    <component
                      :is="stat.changeType === 'increase' ? ArrowUpIcon : ArrowDownIcon"
                      class="self-center flex-shrink-0 h-4 w-4"
                    />
                    <span class="ml-1">{{ stat.change }}%</span>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <router-link :to="stat.link" class="font-medium text-primary-700 hover:text-primary-900">
              View all
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and recent activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Sales chart -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Sales Overview</h3>
        <div class="h-64">
          <Line :data="salesChartData" :options="chartOptions" />
        </div>
      </div>

      <!-- Recent books -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Books</h3>
        <div class="space-y-3">
          <div
            v-for="book in recentBooks"
            :key="book.id"
            class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <img
              :src="book.cover_image_url || '/placeholder-book.jpg'"
              :alt="book.title"
              class="h-12 w-8 object-cover rounded"
            />
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">{{ book.title }}</p>
              <p class="text-sm text-gray-500">{{ formatAuthors(book.authors) }}</p>
              <p class="text-xs text-gray-400">{{ formatDate(book.created_at) }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-900">${{ book.price }}</span>
              <router-link
                :to="{ name: 'admin-books-edit', params: { id: book.id } }"
                class="text-primary-600 hover:text-primary-900"
              >
                <PencilIcon class="h-4 w-4" />
              </router-link>
            </div>
          </div>
        </div>
        <div class="mt-4">
          <router-link
            to="/admin/books"
            class="text-sm font-medium text-primary-600 hover:text-primary-500"
          >
            View all books →
          </router-link>
        </div>
      </div>
    </div>

    <!-- Recent reviews -->
    <div class="bg-white shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Reviews</h3>
      <div class="space-y-4">
        <div
          v-for="review in recentReviews"
          :key="review.id"
          class="border-l-4 border-primary-400 pl-4 py-2"
        >
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-900">{{ review.reviewer_name }}</p>
              <p class="text-xs text-gray-500">{{ review.book_title }}</p>
            </div>
            <div class="flex items-center">
              <StarRating :rating="review.rating" size="sm" />
              <span class="ml-2 text-xs text-gray-500">{{ formatDate(review.created_at) }}</span>
            </div>
          </div>
          <p class="mt-2 text-sm text-gray-700 line-clamp-2">{{ review.review_text }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { fetchDashboardStats, fetchRecentBooks, fetchRecentReviews } from '../../services/adminApi'
import { Line } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import {
  BookOpenIcon,
  UserGroupIcon,
  TagIcon,
  ChatBubbleLeftRightIcon,
  ArrowPathIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PencilIcon
} from '@heroicons/vue/24/outline'
import StarRating from '../../components/StarRating.vue'
import { format } from 'date-fns'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
)

export default {
  name: 'AdminDashboard',
  components: {
    Line,
    StarRating,
    ArrowPathIcon,
    ArrowUpIcon,
    ArrowDownIcon,
    PencilIcon
  },
  setup() {
    const authStore = useAuthStore()
    const loading = ref(false)
    const dashboardData = ref({})
    const recentBooks = ref([])
    const recentReviews = ref([])

    const stats = computed(() => [
      {
        name: 'Total Books',
        value: dashboardData.value.totalBooks || 0,
        icon: BookOpenIcon,
        iconColor: 'text-blue-600',
        change: 12,
        changeType: 'increase',
        link: '/admin/books'
      },
      {
        name: 'Authors',
        value: dashboardData.value.totalAuthors || 0,
        icon: UserGroupIcon,
        iconColor: 'text-green-600',
        change: 5,
        changeType: 'increase',
        link: '/admin/authors'
      },
      {
        name: 'Categories',
        value: dashboardData.value.totalCategories || 0,
        icon: TagIcon,
        iconColor: 'text-yellow-600',
        link: '/admin/categories'
      },
      {
        name: 'Reviews',
        value: dashboardData.value.totalReviews || 0,
        icon: ChatBubbleLeftRightIcon,
        iconColor: 'text-purple-600',
        change: 8,
        changeType: 'increase',
        link: '/admin/reviews'
      }
    ])

    const salesChartData = computed(() => ({
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'Book Sales',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderColor: 'rgb(59, 130, 246)',
          data: [65, 78, 90, 81, 96, 105]
        }
      ]
    }))

    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }

    const formatAuthors = (authors) => {
      if (!authors || authors.length === 0) return 'Unknown Author'
      return authors.map(author => author.name).join(', ')
    }

    const formatDate = (date) => {
      return format(new Date(date), 'MMM dd, yyyy')
    }

    const loadDashboardData = async () => {
      try {
        loading.value = true
        const [stats, books, reviews] = await Promise.all([
          fetchDashboardStats(),
          fetchRecentBooks(),
          fetchRecentReviews()
        ])

        dashboardData.value = stats
        recentBooks.value = books
        recentReviews.value = reviews
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
      } finally {
        loading.value = false
      }
    }

    const refreshData = () => {
      loadDashboardData()
    }

    onMounted(() => {
      loadDashboardData()
    })

    return {
      authStore,
      loading,
      stats,
      recentBooks,
      recentReviews,
      salesChartData,
      chartOptions,
      formatAuthors,
      formatDate,
      refreshData
    }
  }
}
</script>

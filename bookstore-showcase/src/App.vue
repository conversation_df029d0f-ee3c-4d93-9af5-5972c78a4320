<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <Navbar />
    
    <!-- Main Content -->
    <main class="min-h-screen">
      <router-view />
    </main>
    
    <!-- Footer -->
    <Footer />
    
    <!-- Loading Overlay -->
    <LoadingOverlay v-if="isLoading" />
  </div>
</template>

<script>
import { ref, provide } from 'vue'
import Navbar from './components/Navbar.vue'
import Footer from './components/Footer.vue'
import LoadingOverlay from './components/LoadingOverlay.vue'

export default {
  name: 'App',
  components: {
    Navbar,
    Footer,
    LoadingOverlay
  },
  setup() {
    const isLoading = ref(false)
    
    provide('setLoading', (loading) => {
      isLoading.value = loading
    })
    
    return {
      isLoading
    }
  }
}
</script>

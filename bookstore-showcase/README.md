# BookStore Showcase

A modern, responsive bookstore showcase built with Vue.js, Vite, Tailwind CSS, and Neon database integration.

## Features

- 🎨 **Modern UI/UX** - Clean, responsive design with smooth animations
- 📚 **Book Catalog** - Browse books by categories, search, and filter
- ⭐ **Book Details** - Detailed book information with ratings and reviews
- 🔍 **Advanced Search** - Search by title, author, or ISBN
- 📱 **Mobile Responsive** - Optimized for all device sizes
- 🚀 **Performance** - Fast loading with code splitting and lazy loading
- 🔧 **SEO Optimized** - Meta tags, structured data, and semantic HTML
- 🎭 **Smooth Animations** - Custom CSS animations with Tailwind

## Tech Stack

### Frontend
- **Vue.js 3** - Progressive JavaScript framework
- **Vite** - Fast build tool and dev server
- **Vue Router** - Client-side routing
- **Tailwind CSS** - Utility-first CSS framework
- **Axios** - HTTP client for API calls

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **PostgreSQL** - Database (via Neon)
- **Neon** - Serverless PostgreSQL platform

## Project Structure

```
bookstore-showcase/
├── public/                 # Static assets
├── src/
│   ├── components/        # Vue components
│   │   ├── BookCard.vue
│   │   ├── Navbar.vue
│   │   ├── Footer.vue
│   │   └── ...
│   ├── pages/            # Page components
│   │   ├── Home.vue
│   │   ├── BookDetail.vue
│   │   ├── Search.vue
│   │   └── Category.vue
│   ├── services/         # API services
│   │   └── api.js
│   ├── App.vue          # Root component
│   ├── main.js          # Application entry point
│   └── style.css        # Global styles
├── server/              # Backend server
│   └── index.js
├── database/            # Database scripts
│   ├── schema.sql
│   └── seed_data.sql
└── ...
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- PostgreSQL database (Neon account recommended)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bookstore-showcase
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your database connection string:
   ```
   NEON_DATABASE_URL=postgresql://username:password@hostname:port/database?sslmode=require
   VITE_API_BASE_URL=http://localhost:3001/api
   PORT=3001
   ```

4. **Set up the database**
   
   Run the SQL scripts in your Neon database:
   ```bash
   # First, run the schema
   psql $NEON_DATABASE_URL -f database/schema.sql
   
   # Then, run the seed data
   psql $NEON_DATABASE_URL -f database/seed_data.sql
   ```

5. **Start the development servers**
   
   In one terminal, start the backend server:
   ```bash
   npm run server
   ```
   
   In another terminal, start the frontend development server:
   ```bash
   npm run dev
   ```

6. **Open your browser**
   
   Navigate to `http://localhost:3000` to see the application.

## Available Scripts

- `npm run dev` - Start the frontend development server
- `npm run build` - Build the frontend for production
- `npm run preview` - Preview the production build
- `npm run server` - Start the backend server

## Database Setup

### Using Neon (Recommended)

1. Create a free account at [Neon](https://neon.tech)
2. Create a new project and database
3. Copy the connection string to your `.env` file
4. Run the SQL scripts to set up the schema and seed data

### Using Local PostgreSQL

1. Install PostgreSQL locally
2. Create a new database
3. Update the connection string in `.env`
4. Run the SQL scripts

## API Endpoints

- `GET /api/health` - Health check
- `GET /api/books` - Get all books (with pagination, search, filters)
- `GET /api/books/:id` - Get book by ID
- `GET /api/categories` - Get all categories

## Customization

### Adding New Books

You can add new books by inserting data into the database tables:

1. Add authors to the `authors` table
2. Add publishers to the `publishers` table
3. Add books to the `books` table
4. Link books to authors in `book_authors` table
5. Link books to categories in `book_categories` table

### Styling

The project uses Tailwind CSS for styling. You can customize:

- Colors in `tailwind.config.js`
- Animations in `src/style.css`
- Component styles in individual Vue files

### Adding Features

The project is structured to be easily extensible:

- Add new pages in `src/pages/`
- Add new components in `src/components/`
- Add new API endpoints in `server/index.js`
- Add new database tables in `database/schema.sql`

## SEO Features

- Meta tags for social sharing
- Structured data (JSON-LD)
- Semantic HTML structure
- Clean URLs
- Image optimization
- Performance optimization

## Performance Features

- Code splitting
- Lazy loading
- Image optimization
- Efficient animations
- Minimal bundle size

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue on GitHub or contact the development team.

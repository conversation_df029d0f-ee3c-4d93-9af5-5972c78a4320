import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { adminLogin, adminLogout, getAdminProfile } from '../services/adminApi'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('admin_token'))
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  const login = async (credentials) => {
    try {
      loading.value = true
      const response = await adminLogin(credentials)
      
      token.value = response.token
      user.value = response.user
      
      localStorage.setItem('admin_token', response.token)
      return response
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      await adminLogout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('admin_token')
    }
  }

  const fetchProfile = async () => {
    try {
      if (!token.value) return
      
      const profile = await getAdminProfile()
      user.value = profile
    } catch (error) {
      console.error('Failed to fetch profile:', error)
      await logout()
    }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    fetchProfile
  }
})

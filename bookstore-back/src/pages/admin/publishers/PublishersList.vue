<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Publishers Management
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Manage book publishers
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <router-link
            :to="{ name: 'admin-publishers-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Publisher
          </router-link>
        </div>
      </div>
    </div>

    <!-- Search -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="max-w-md">
        <label class="block text-sm font-medium text-gray-700 mb-1">Search Publishers</label>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search by name..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          @input="debouncedSearch"
        />
      </div>
    </div>

    <!-- Publishers table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Publishers ({{ pagination.total }})
        </h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading publishers...</p>
      </div>
      
      <div v-else-if="publishers.length === 0" class="p-8 text-center">
        <BuildingOfficeIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No publishers found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding a new publisher.</p>
        <div class="mt-6">
          <router-link
            :to="{ name: 'admin-publishers-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Publisher
          </router-link>
        </div>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Publisher
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Books Count
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Founded
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="publisher in publishers" :key="publisher.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <div class="h-10 w-10 rounded-lg bg-primary-100 flex items-center justify-center">
                      <BuildingOfficeIcon class="h-6 w-6 text-primary-600" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ publisher.name }}</div>
                    <div v-if="publisher.website" class="text-sm text-gray-500">
                      <a :href="publisher.website" target="_blank" class="hover:text-primary-600">
                        {{ publisher.website }}
                      </a>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div v-if="publisher.address || publisher.city || publisher.country">
                  <div v-if="publisher.city || publisher.country">
                    {{ [publisher.city, publisher.country].filter(Boolean).join(', ') }}
                  </div>
                  <div v-if="publisher.address" class="text-xs text-gray-500">
                    {{ publisher.address }}
                  </div>
                </div>
                <span v-else class="text-gray-400">-</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ publisher.books_count || 0 }} books
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ publisher.founded_year || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <router-link
                    :to="{ name: 'admin-publishers-edit', params: { id: publisher.id } }"
                    class="text-primary-600 hover:text-primary-900"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </router-link>
                  <button
                    @click="deletePublisher(publisher)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <AdminPagination
        v-if="publishers.length > 0"
        :current-page="pagination.page"
        :total-pages="pagination.totalPages"
        :total-items="pagination.total"
        @page-changed="changePage"
      />
    </div>

    <!-- Delete confirmation modal -->
    <ConfirmationModal
      v-if="publisherToDelete"
      title="Delete Publisher"
      :message="`Are you sure you want to delete '${publisherToDelete.name}'? This action cannot be undone.`"
      @confirm="confirmDelete"
      @cancel="publisherToDelete = null"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { fetchPublishers, deletePublisher as deletePublisherApi } from '../../../services/adminApi'
import { PlusIcon, BuildingOfficeIcon, PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'
import AdminPagination from '../../../components/admin/AdminPagination.vue'
import ConfirmationModal from '../../../components/admin/ConfirmationModal.vue'
import { useToast } from 'vue-toastification'
import { debounce } from 'lodash-es'

export default {
  name: 'PublishersList',
  components: {
    PlusIcon,
    BuildingOfficeIcon,
    PencilIcon,
    TrashIcon,
    AdminPagination,
    ConfirmationModal
  },
  setup() {
    const toast = useToast()
    const loading = ref(false)
    const publishers = ref([])
    const publisherToDelete = ref(null)
    const searchQuery = ref('')
    
    const pagination = ref({
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    })

    const loadPublishers = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.value.page,
          limit: pagination.value.limit
        }
        
        if (searchQuery.value) {
          params.search = searchQuery.value
        }
        
        const response = await fetchPublishers(params)
        
        publishers.value = response.publishers || response
        pagination.value = {
          page: response.page || 1,
          limit: response.limit || 10,
          total: response.total || publishers.value.length,
          totalPages: response.totalPages || Math.ceil((response.total || publishers.value.length) / (response.limit || 10))
        }
      } catch (error) {
        console.error('Failed to load publishers:', error)
        toast.error('Failed to load publishers')
      } finally {
        loading.value = false
      }
    }

    const debouncedSearch = debounce(() => {
      pagination.value.page = 1
      loadPublishers()
    }, 500)

    const changePage = (page) => {
      pagination.value.page = page
      loadPublishers()
    }

    const deletePublisher = (publisher) => {
      publisherToDelete.value = publisher
    }

    const confirmDelete = async () => {
      try {
        await deletePublisherApi(publisherToDelete.value.id)
        toast.success('Publisher deleted successfully')
        publisherToDelete.value = null
        loadPublishers()
      } catch (error) {
        toast.error('Failed to delete publisher')
      }
    }

    onMounted(() => {
      loadPublishers()
    })

    return {
      loading,
      publishers,
      publisherToDelete,
      searchQuery,
      pagination,
      debouncedSearch,
      changePage,
      deletePublisher,
      confirmDelete
    }
  }
}
</script>

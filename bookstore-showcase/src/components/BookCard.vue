<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 animate-fade-in">
    <!-- Book Cover -->
    <div class="relative overflow-hidden">
      <img
        :src="book.cover_image_url || '/placeholder-book.jpg'"
        :alt="book.title"
        class="w-full h-64 object-cover transition-transform duration-300 hover:scale-105"
        @error="handleImageError"
      >
      
      <!-- Featured Badge -->
      <div v-if="book.featured" class="absolute top-2 left-2">
        <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium animate-pulse-slow">
          Featured
        </span>
      </div>
      
      <!-- Price Tag -->
      <div class="absolute top-2 right-2">
        <span class="bg-primary-600 text-white px-2 py-1 rounded-full text-sm font-medium">
          ${{ book.price }}
        </span>
      </div>
      
      <!-- Stock Status -->
      <div v-if="!book.in_stock" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
        <span class="bg-red-600 text-white px-4 py-2 rounded-lg font-medium">
          Out of Stock
        </span>
      </div>
    </div>

    <!-- Book Info -->
    <div class="p-4">
      <!-- Title -->
      <h3 class="font-semibold text-lg text-gray-900 mb-2 line-clamp-2 hover:text-primary-600 transition-colors duration-200">
        <router-link :to="`/book/${book.id}`">
          {{ book.title }}
        </router-link>
      </h3>
      
      <!-- Subtitle -->
      <p v-if="book.subtitle" class="text-sm text-gray-600 mb-2 line-clamp-1">
        {{ book.subtitle }}
      </p>
      
      <!-- Authors -->
      <p class="text-sm text-gray-700 mb-2">
        by {{ formatAuthors(book.authors) }}
      </p>
      
      <!-- Rating -->
      <div class="flex items-center mb-3">
        <div class="flex items-center">
          <star-rating :rating="book.rating" />
          <span class="ml-2 text-sm text-gray-600">
            ({{ book.review_count }} {{ book.review_count === 1 ? 'review' : 'reviews' }})
          </span>
        </div>
      </div>
      
      <!-- Description -->
      <p class="text-gray-600 text-sm mb-4 line-clamp-3">
        {{ book.description }}
      </p>
      
      <!-- Actions -->
      <div class="flex justify-between items-center">
        <router-link
          :to="`/book/${book.id}`"
          class="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200 transform hover:scale-105"
        >
          View Details
        </router-link>
        
        <button
          @click="addToWishlist"
          class="p-2 text-gray-400 hover:text-red-500 transition-colors duration-200 transform hover:scale-110"
          :class="{ 'text-red-500': isInWishlist }"
        >
          <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import StarRating from './StarRating.vue'

export default {
  name: 'BookCard',
  components: {
    StarRating
  },
  props: {
    book: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const isInWishlist = ref(false)

    const formatAuthors = (authors) => {
      if (!authors || authors.length === 0) return 'Unknown Author'
      return authors.map(author => author.name).join(', ')
    }

    const handleImageError = (event) => {
      event.target.src = '/placeholder-book.jpg'
    }

    const addToWishlist = () => {
      isInWishlist.value = !isInWishlist.value
      // Here you would typically make an API call to add/remove from wishlist
    }

    return {
      isInWishlist,
      formatAuthors,
      handleImageError,
      addToWishlist
    }
  }
}
</script>

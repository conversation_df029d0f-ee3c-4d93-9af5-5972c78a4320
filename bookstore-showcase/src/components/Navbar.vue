<template>
  <nav class="bg-white shadow-lg sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <router-link to="/" class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <span class="text-xl font-bold text-gray-900">BookStore</span>
        </router-link>

        <!-- Search Bar -->
        <div class="flex-1 max-w-lg mx-8">
          <div class="relative">
            <input
              v-model="searchQuery"
              @keyup.enter="performSearch"
              type="text"
              placeholder="Search books, authors, or ISBN..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
          </div>
        </div>

        <!-- Navigation Links -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link to="/" class="text-gray-700 hover:text-primary-600 transition-colors duration-200">
            Home
          </router-link>
          <div class="relative" @mouseover="showCategories = true" @mouseleave="showCategories = false">
            <button class="text-gray-700 hover:text-primary-600 transition-colors duration-200 flex items-center">
              Categories
              <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </button>
            
            <!-- Categories Dropdown -->
            <div v-show="showCategories" class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 animate-fade-in">
              <router-link
                v-for="category in categories"
                :key="category.id"
                :to="`/category/${category.slug}`"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200"
              >
                {{ category.name }}
              </router-link>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div v-show="mobileMenuOpen" class="md:hidden bg-white border-t border-gray-200 animate-slide-up">
      <div class="px-2 pt-2 pb-3 space-y-1">
        <router-link to="/" class="block px-3 py-2 text-gray-700 hover:bg-primary-50 rounded-md">
          Home
        </router-link>
        <div class="px-3 py-2">
          <div class="text-sm font-medium text-gray-500 mb-2">Categories</div>
          <router-link
            v-for="category in categories"
            :key="category.id"
            :to="`/category/${category.slug}`"
            class="block px-3 py-1 text-sm text-gray-600 hover:text-primary-600"
          >
            {{ category.name }}
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { fetchCategories } from '../services/api'

export default {
  name: 'Navbar',
  setup() {
    const router = useRouter()
    const searchQuery = ref('')
    const showCategories = ref(false)
    const mobileMenuOpen = ref(false)
    const categories = ref([])

    const performSearch = () => {
      if (searchQuery.value.trim()) {
        router.push({ name: 'search', query: { q: searchQuery.value } })
      }
    }

    const loadCategories = async () => {
      try {
        categories.value = await fetchCategories()
      } catch (error) {
        console.error('Failed to load categories:', error)
      }
    }

    onMounted(() => {
      loadCategories()
    })

    return {
      searchQuery,
      showCategories,
      mobileMenuOpen,
      categories,
      performSearch
    }
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Authors Management
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            Manage book authors
          </p>
        </div>
        <div class="mt-4 flex md:mt-0 md:ml-4">
          <router-link
            :to="{ name: 'admin-authors-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Author
          </router-link>
        </div>
      </div>
    </div>

    <!-- Search -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="max-w-md">
        <label class="block text-sm font-medium text-gray-700 mb-1">Search Authors</label>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search by name..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          @input="debouncedSearch"
        />
      </div>
    </div>

    <!-- Authors table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
          Authors ({{ pagination.total }})
        </h3>
      </div>
      
      <div v-if="loading" class="p-8 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading authors...</p>
      </div>
      
      <div v-else-if="authors.length === 0" class="p-8 text-center">
        <UserGroupIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No authors found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding a new author.</p>
        <div class="mt-6">
          <router-link
            :to="{ name: 'admin-authors-create' }"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Author
          </router-link>
        </div>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Author
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Books Count
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="author in authors" :key="author.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                      <span class="text-sm font-medium text-primary-700">
                        {{ getInitials(author.name) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ author.name }}</div>
                    <div v-if="author.bio" class="text-sm text-gray-500 line-clamp-1">{{ author.bio }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ author.books_count || 0 }} books
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(author.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <router-link
                    :to="{ name: 'admin-authors-edit', params: { id: author.id } }"
                    class="text-primary-600 hover:text-primary-900"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </router-link>
                  <button
                    @click="deleteAuthor(author)"
                    class="text-red-600 hover:text-red-900"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <AdminPagination
        v-if="authors.length > 0"
        :current-page="pagination.page"
        :total-pages="pagination.totalPages"
        :total-items="pagination.total"
        @page-changed="changePage"
      />
    </div>

    <!-- Delete confirmation modal -->
    <ConfirmationModal
      v-if="authorToDelete"
      title="Delete Author"
      :message="`Are you sure you want to delete '${authorToDelete.name}'? This action cannot be undone.`"
      @confirm="confirmDelete"
      @cancel="authorToDelete = null"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { fetchAuthors, deleteAuthor as deleteAuthorApi } from '../../../services/adminApi'
import { PlusIcon, UserGroupIcon, PencilIcon, TrashIcon } from '@heroicons/vue/24/outline'
import AdminPagination from '../../../components/admin/AdminPagination.vue'
import ConfirmationModal from '../../../components/admin/ConfirmationModal.vue'
import { useToast } from 'vue-toastification'
import { debounce } from 'lodash-es'
import { format } from 'date-fns'

export default {
  name: 'AuthorsList',
  components: {
    PlusIcon,
    UserGroupIcon,
    PencilIcon,
    TrashIcon,
    AdminPagination,
    ConfirmationModal
  },
  setup() {
    const toast = useToast()
    const loading = ref(false)
    const authors = ref([])
    const authorToDelete = ref(null)
    const searchQuery = ref('')
    
    const pagination = ref({
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    })

    const getInitials = (name) => {
      return name.split(' ').map(n => n[0]).join('').toUpperCase()
    }

    const formatDate = (date) => {
      return format(new Date(date), 'MMM dd, yyyy')
    }

    const loadAuthors = async () => {
      try {
        loading.value = true
        const params = {
          page: pagination.value.page,
          limit: pagination.value.limit
        }
        
        if (searchQuery.value) {
          params.search = searchQuery.value
        }
        
        const response = await fetchAuthors(params)
        
        authors.value = response.authors || response
        pagination.value = {
          page: response.page || 1,
          limit: response.limit || 10,
          total: response.total || authors.value.length,
          totalPages: response.totalPages || Math.ceil((response.total || authors.value.length) / (response.limit || 10))
        }
      } catch (error) {
        console.error('Failed to load authors:', error)
        toast.error('Failed to load authors')
      } finally {
        loading.value = false
      }
    }

    const debouncedSearch = debounce(() => {
      pagination.value.page = 1
      loadAuthors()
    }, 500)

    const changePage = (page) => {
      pagination.value.page = page
      loadAuthors()
    }

    const deleteAuthor = (author) => {
      authorToDelete.value = author
    }

    const confirmDelete = async () => {
      try {
        await deleteAuthorApi(authorToDelete.value.id)
        toast.success('Author deleted successfully')
        authorToDelete.value = null
        loadAuthors()
      } catch (error) {
        toast.error('Failed to delete author')
      }
    }

    onMounted(() => {
      loadAuthors()
    })

    return {
      loading,
      authors,
      authorToDelete,
      searchQuery,
      pagination,
      getInitials,
      formatDate,
      debouncedSearch,
      changePage,
      deleteAuthor,
      confirmDelete
    }
  }
}
</script>

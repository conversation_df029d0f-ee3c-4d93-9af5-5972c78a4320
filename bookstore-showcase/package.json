{"name": "bookstore-showcase", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server/index.js"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "@vueuse/core": "^10.7.0", "axios": "^1.6.0", "pg": "^8.11.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "express": "^4.18.0", "cors": "^2.8.5", "dotenv": "^16.3.0"}}
# BookStore Admin Panel

A comprehensive admin panel for managing the BookStore application built with Vue 3, Vite, and Tailwind CSS.

## Features

- **Dashboard**: Overview with statistics and charts
- **Books Management**: Full CRUD operations for books
- **Authors Management**: Manage book authors
- **Categories Management**: Organize books by categories
- **Publishers Management**: Manage book publishers
- **Reviews Management**: Moderate and manage book reviews
- **Settings**: Admin profile and application settings
- **Authentication**: Secure login system with JWT

## Tech Stack

- **Frontend**: Vue 3, Vite, Tailwind CSS
- **State Management**: Pinia
- **Routing**: Vue Router
- **UI Components**: Heroicons, Headless UI
- **Charts**: Chart.js with Vue-ChartJS
- **HTTP Client**: Axios
- **Notifications**: Vue Toastification
- **Backend**: Node.js, Express
- **Database**: PostgreSQL
- **Authentication**: JWT

## Prerequisites

- Node.js (v16 or higher)
- PostgreSQL database
- The main bookstore database should be set up (see ../bookstore-showcase/database/)

## Installation

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file in the root directory:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bookstore
DB_USER=postgres
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your-secret-key

# Server Configuration
PORT=3002
```

3. Make sure your PostgreSQL database is running and the bookstore database exists with the proper schema.

## Development

1. Start the backend server:
```bash
npm run server
```

2. In a new terminal, start the frontend development server:
```bash
npm run dev
```

3. Open your browser and navigate to `http://localhost:3001`

## Default Admin Credentials

- **Email**: <EMAIL>
- **Password**: admin123

## Project Structure

```
src/
├── components/           # Reusable components
│   ├── admin/           # Admin-specific components
│   └── StarRating.vue   # Star rating component
├── layouts/             # Layout components
│   └── AdminLayout.vue  # Main admin layout
├── pages/               # Page components
│   └── admin/           # Admin pages
│       ├── books/       # Books management
│       ├── authors/     # Authors management
│       ├── categories/  # Categories management
│       ├── publishers/  # Publishers management
│       ├── reviews/     # Reviews management
│       ├── Dashboard.vue
│       ├── Login.vue
│       └── Settings.vue
├── router/              # Vue Router configuration
├── services/            # API services
├── stores/              # Pinia stores
└── style.css           # Global styles
```

## API Endpoints

### Authentication
- `POST /api/admin/auth/login` - Admin login
- `POST /api/admin/auth/logout` - Admin logout
- `GET /api/admin/auth/profile` - Get admin profile

### Dashboard
- `GET /api/admin/dashboard/stats` - Get dashboard statistics
- `GET /api/admin/dashboard/recent-books` - Get recent books
- `GET /api/admin/dashboard/recent-reviews` - Get recent reviews

### Books
- `GET /api/admin/books` - List books with pagination and filters
- `GET /api/admin/books/:id` - Get single book
- `POST /api/admin/books` - Create new book
- `PUT /api/admin/books/:id` - Update book
- `DELETE /api/admin/books/:id` - Delete book

### Authors
- `GET /api/admin/authors` - List authors
- `GET /api/admin/authors/:id` - Get single author
- `POST /api/admin/authors` - Create new author
- `PUT /api/admin/authors/:id` - Update author
- `DELETE /api/admin/authors/:id` - Delete author

### Categories
- `GET /api/admin/categories` - List categories
- `GET /api/admin/categories/:id` - Get single category
- `POST /api/admin/categories` - Create new category
- `PUT /api/admin/categories/:id` - Update category
- `DELETE /api/admin/categories/:id` - Delete category

### Publishers
- `GET /api/admin/publishers` - List publishers
- `GET /api/admin/publishers/:id` - Get single publisher
- `POST /api/admin/publishers` - Create new publisher
- `PUT /api/admin/publishers/:id` - Update publisher
- `DELETE /api/admin/publishers/:id` - Delete publisher

### Reviews
- `GET /api/admin/reviews` - List reviews with filters
- `DELETE /api/admin/reviews/:id` - Delete review
- `PATCH /api/admin/reviews/:id/status` - Update review status

## Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Features Overview

### Dashboard
- Statistics cards showing total books, authors, categories, and reviews
- Sales overview chart
- Recent books list
- Recent reviews list

### Books Management
- Comprehensive book form with all fields
- Multiple authors support
- Category assignment
- Image upload support
- Stock management
- Featured books toggle
- Advanced filtering and search

### Authors Management
- Author profile management
- Biography support
- Book count tracking

### Categories Management
- Category creation and editing
- Slug generation
- Book count per category

### Publishers Management
- Publisher information management
- Contact details
- Location information

### Reviews Management
- Review moderation
- Status management (approved/pending/rejected)
- Rating filters
- Bulk actions

### Settings
- Admin profile management
- Password change
- Application settings
- Notification preferences

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

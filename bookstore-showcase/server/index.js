import express from 'express'
import cors from 'cors'
import { Pool } from 'pg'
import dotenv from 'dotenv'

dotenv.config()

const app = express()
const port = process.env.PORT || 3001

// Neon database connection
const pool = new Pool({
  connectionString: process.env.NEON_DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
})

app.use(cors())
app.use(express.json())

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'BookStore API is running' })
})

// Books endpoints
app.get('/api/books', async (req, res) => {
  try {
    const { page = 1, limit = 10, category, search, featured } = req.query
    const offset = (page - 1) * limit
    
    let query = `
      SELECT b.*, 
             array_agg(DISTINCT jsonb_build_object('id', a.id, 'name', a.name)) as authors,
             array_agg(DISTINCT jsonb_build_object('id', c.id, 'name', c.name, 'slug', c.slug)) as categories,
             jsonb_build_object('id', p.id, 'name', p.name) as publisher
      FROM books b
      LEFT JOIN book_authors ba ON b.id = ba.book_id
      LEFT JOIN authors a ON ba.author_id = a.id
      LEFT JOIN book_categories bc ON b.id = bc.book_id
      LEFT JOIN categories c ON bc.category_id = c.id
      LEFT JOIN publishers p ON b.publisher_id = p.id
      WHERE 1=1
    `
    
    const params = []
    let paramCount = 0
    
    if (featured) {
      query += ` AND b.featured = $${++paramCount}`
      params.push(true)
    }
    
    if (category) {
      query += ` AND c.slug = $${++paramCount}`
      params.push(category)
    }
    
    if (search) {
      query += ` AND (b.title ILIKE $${++paramCount} OR a.name ILIKE $${++paramCount})`
      params.push(`%${search}%`, `%${search}%`)
      paramCount++
    }
    
    query += `
      GROUP BY b.id, p.id
      ORDER BY b.created_at DESC
      LIMIT $${++paramCount} OFFSET $${++paramCount}
    `
    
    params.push(limit, offset)
    
    const result = await pool.query(query, params)
    
    // Get total count
    let countQuery = 'SELECT COUNT(DISTINCT b.id) FROM books b'
    if (category || search) {
      countQuery += ' LEFT JOIN book_categories bc ON b.id = bc.book_id LEFT JOIN categories c ON bc.category_id = c.id'
      if (search) {
        countQuery += ' LEFT JOIN book_authors ba ON b.id = ba.book_id LEFT JOIN authors a ON ba.author_id = a.id'
      }
      countQuery += ' WHERE 1=1'
      
      const countParams = []
      let countParamCount = 0
      
      if (featured) {
        countQuery += ` AND b.featured = $${++countParamCount}`
        countParams.push(true)
      }
      
      if (category) {
        countQuery += ` AND c.slug = $${++countParamCount}`
        countParams.push(category)
      }
      
      if (search) {
        countQuery += ` AND (b.title ILIKE $${++countParamCount} OR a.name ILIKE $${++countParamCount})`
        countParams.push(`%${search}%`, `%${search}%`)
        countParamCount++
      }
      
      const countResult = await pool.query(countQuery, countParams)
      const total = parseInt(countResult.rows[0].count)
      
      res.json({
        books: result.rows,
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      })
    } else {
      const countResult = await pool.query('SELECT COUNT(*) FROM books')
      const total = parseInt(countResult.rows[0].count)
      
      res.json({
        books: result.rows,
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      })
    }
  } catch (error) {
    console.error('Error fetching books:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.get('/api/books/:id', async (req, res) => {
  try {
    const { id } = req.params
    
    const query = `
      SELECT b.*, 
             array_agg(DISTINCT jsonb_build_object('id', a.id, 'name', a.name, 'bio', a.bio)) as authors,
             array_agg(DISTINCT jsonb_build_object('id', c.id, 'name', c.name, 'slug', c.slug)) as categories,
             jsonb_build_object('id', p.id, 'name', p.name, 'website', p.website) as publisher
      FROM books b
      LEFT JOIN book_authors ba ON b.id = ba.book_id
      LEFT JOIN authors a ON ba.author_id = a.id
      LEFT JOIN book_categories bc ON b.id = bc.book_id
      LEFT JOIN categories c ON bc.category_id = c.id
      LEFT JOIN publishers p ON b.publisher_id = p.id
      WHERE b.id = $1
      GROUP BY b.id, p.id
    `
    
    const result = await pool.query(query, [id])
    
    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Book not found' })
    }
    
    res.json(result.rows[0])
  } catch (error) {
    console.error('Error fetching book:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

app.get('/api/categories', async (req, res) => {
  try {
    const result = await pool.query('SELECT * FROM categories ORDER BY name')
    res.json(result.rows)
  } catch (error) {
    console.error('Error fetching categories:', error)
    res.status(500).json({ error: 'Internal server error' })
  }
})

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: 'Something went wrong!' })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' })
})

app.listen(port, () => {
  console.log(`Server running on port ${port}`)
  console.log(`Health check: http://localhost:${port}/api/health`)
})

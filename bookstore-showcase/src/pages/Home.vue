<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <HeroSection />
    
    <!-- Featured Books -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4 animate-fade-in">
            Featured Books
          </h2>
          <p class="text-lg text-gray-600 animate-fade-in">
            Discover our handpicked selection of exceptional reads
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <BookCard
            v-for="book in featuredBooks"
            :key="book.id"
            :book="book"
            class="animate-slide-up"
          />
        </div>
      </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            Browse by Category
          </h2>
          <p class="text-lg text-gray-600">
            Find your next great read in your favorite genre
          </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <CategoryCard
            v-for="category in categories"
            :key="category.id"
            :category="category"
            class="animate-fade-in"
          />
        </div>
      </div>
    </section>

    <!-- Latest Books -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            Latest Additions
          </h2>
          <p class="text-lg text-gray-600">
            Fresh arrivals to our collection
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <BookCard
            v-for="book in latestBooks"
            :key="book.id"
            :book="book"
            class="animate-slide-up"
          />
        </div>
        
        <div class="text-center mt-12">
          <router-link
            to="/search"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors duration-200"
          >
            View All Books
            <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
            </svg>
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted, inject } from 'vue'
import BookCard from '../components/BookCard.vue'
import CategoryCard from '../components/CategoryCard.vue'
import HeroSection from '../components/HeroSection.vue'
import { fetchFeaturedBooks, fetchLatestBooks, fetchCategories } from '../services/api'

export default {
  name: 'Home',
  components: {
    BookCard,
    CategoryCard,
    HeroSection
  },
  setup() {
    const setLoading = inject('setLoading')
    const featuredBooks = ref([])
    const latestBooks = ref([])
    const categories = ref([])

    const loadData = async () => {
      try {
        setLoading(true)
        const [featured, latest, cats] = await Promise.all([
          fetchFeaturedBooks(),
          fetchLatestBooks(),
          fetchCategories()
        ])
        
        featuredBooks.value = featured
        latestBooks.value = latest
        categories.value = cats
      } catch (error) {
        console.error('Failed to load home page data:', error)
      } finally {
        setLoading(false)
      }
    }

    onMounted(() => {
      loadData()
    })

    return {
      featuredBooks,
      latestBooks,
      categories
    }
  }
}
</script>

import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

// Admin Layout
import AdminLayout from '../layouts/AdminLayout.vue'

// Admin Pages
import AdminDashboard from '../pages/admin/Dashboard.vue'
import BooksList from '../pages/admin/books/BooksList.vue'
import BookForm from '../pages/admin/books/BookForm.vue'
import AuthorsList from '../pages/admin/authors/AuthorsList.vue'
import AuthorForm from '../pages/admin/authors/AuthorForm.vue'
import CategoriesList from '../pages/admin/categories/CategoriesList.vue'
import CategoryForm from '../pages/admin/categories/CategoryForm.vue'
import PublishersList from '../pages/admin/publishers/PublishersList.vue'
import PublisherForm from '../pages/admin/publishers/PublisherForm.vue'
import ReviewsList from '../pages/admin/reviews/ReviewsList.vue'
import AdminSettings from '../pages/admin/Settings.vue'
import AdminLogin from '../pages/admin/Login.vue'

const routes = [
  {
    path: '/admin/login',
    name: 'admin-login',
    component: AdminLogin,
    meta: { requiresGuest: true }
  },
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: AdminDashboard
      },
      {
        path: 'books',
        name: 'admin-books',
        component: BooksList
      },
      {
        path: 'books/create',
        name: 'admin-books-create',
        component: BookForm
      },
      {
        path: 'books/:id/edit',
        name: 'admin-books-edit',
        component: BookForm,
        props: true
      },
      {
        path: 'authors',
        name: 'admin-authors',
        component: AuthorsList
      },
      {
        path: 'authors/create',
        name: 'admin-authors-create',
        component: AuthorForm
      },
      {
        path: 'authors/:id/edit',
        name: 'admin-authors-edit',
        component: AuthorForm,
        props: true
      },
      {
        path: 'categories',
        name: 'admin-categories',
        component: CategoriesList
      },
      {
        path: 'categories/create',
        name: 'admin-categories-create',
        component: CategoryForm
      },
      {
        path: 'categories/:id/edit',
        name: 'admin-categories-edit',
        component: CategoryForm,
        props: true
      },
      {
        path: 'publishers',
        name: 'admin-publishers',
        component: PublishersList
      },
      {
        path: 'publishers/create',
        name: 'admin-publishers-create',
        component: PublisherForm
      },
      {
        path: 'publishers/:id/edit',
        name: 'admin-publishers-edit',
        component: PublisherForm,
        props: true
      },
      {
        path: 'reviews',
        name: 'admin-reviews',
        component: ReviewsList
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: AdminSettings
      }
    ]
  },
  {
    path: '/',
    redirect: '/admin'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'admin-login' })
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next({ name: 'admin-dashboard' })
  } else {
    next()
  }
})

export default router

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div v-if="book" class="bg-white rounded-lg shadow-lg overflow-hidden animate-fade-in">
        <div class="md:flex">
          <!-- Book Cover -->
          <div class="md:w-1/3 lg:w-1/4">
            <img
              :src="book.cover_image_url || '/placeholder-book.jpg'"
              :alt="book.title"
              class="w-full h-96 md:h-full object-cover"
              @error="handleImageError"
            >
          </div>

          <!-- Book Details -->
          <div class="md:w-2/3 lg:w-3/4 p-8">
            <div class="flex flex-col h-full">
              <!-- Header -->
              <div class="mb-6">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {{ book.title }}
                </h1>
                <p v-if="book.subtitle" class="text-xl text-gray-600 mb-4">
                  {{ book.subtitle }}
                </p>
                <p class="text-lg text-gray-700 mb-4">
                  by {{ formatAuthors(book.authors) }}
                </p>
                
                <!-- Rating and Reviews -->
                <div class="flex items-center mb-4">
                  <StarRating :rating="book.rating" />
                  <span class="ml-3 text-gray-600">
                    {{ book.review_count }} {{ book.review_count === 1 ? 'review' : 'reviews' }}
                  </span>
                </div>

                <!-- Categories -->
                <div class="flex flex-wrap gap-2 mb-4">
                  <router-link
                    v-for="category in book.categories"
                    :key="category.id"
                    :to="`/category/${category.slug}`"
                    class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm hover:bg-primary-200 transition-colors duration-200"
                  >
                    {{ category.name }}
                  </router-link>
                </div>
              </div>

              <!-- Description -->
              <div class="mb-6 flex-grow">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                <p class="text-gray-700 leading-relaxed">
                  {{ book.description }}
                </p>
              </div>

              <!-- Book Details Grid -->
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 text-sm">
                <div>
                  <span class="font-medium text-gray-900">ISBN:</span>
                  <p class="text-gray-600">{{ book.isbn || 'N/A' }}</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Pages:</span>
                  <p class="text-gray-600">{{ book.pages || 'N/A' }}</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Language:</span>
                  <p class="text-gray-600">{{ book.language || 'English' }}</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Format:</span>
                  <p class="text-gray-600">{{ book.format || 'Paperback' }}</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Published:</span>
                  <p class="text-gray-600">{{ formatDate(book.publication_date) }}</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Publisher:</span>
                  <p class="text-gray-600">{{ book.publisher?.name || 'N/A' }}</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Stock:</span>
                  <p class="text-gray-600">{{ book.stock_quantity || 0 }} available</p>
                </div>
                <div>
                  <span class="font-medium text-gray-900">Price:</span>
                  <p class="text-2xl font-bold text-primary-600">${{ book.price }}</p>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex flex-col sm:flex-row gap-4">
                <button
                  :disabled="!book.in_stock"
                  class="flex-1 bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {{ book.in_stock ? 'Add to Cart' : 'Out of Stock' }}
                </button>
                <button
                  @click="addToWishlist"
                  class="flex-1 border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200"
                  :class="{ 'bg-primary-50': isInWishlist }"
                >
                  {{ isInWishlist ? 'Remove from Wishlist' : 'Add to Wishlist' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="flex justify-center items-center h-64">
        <div class="spinner"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, inject } from 'vue'
import { useRoute } from 'vue-router'
import StarRating from '../components/StarRating.vue'
import { fetchBookById } from '../services/api'

export default {
  name: 'BookDetail',
  components: {
    StarRating
  },
  setup() {
    const route = useRoute()
    const setLoading = inject('setLoading')
    const book = ref(null)
    const isInWishlist = ref(false)

    const formatAuthors = (authors) => {
      if (!authors || authors.length === 0) return 'Unknown Author'
      return authors.map(author => author.name).join(', ')
    }

    const formatDate = (dateString) => {
      if (!dateString) return 'N/A'
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const handleImageError = (event) => {
      event.target.src = '/placeholder-book.jpg'
    }

    const addToWishlist = () => {
      isInWishlist.value = !isInWishlist.value
      // Here you would typically make an API call to add/remove from wishlist
    }

    const loadBook = async () => {
      try {
        setLoading(true)
        const bookData = await fetchBookById(route.params.id)
        book.value = bookData
      } catch (error) {
        console.error('Failed to load book:', error)
      } finally {
        setLoading(false)
      }
    }

    onMounted(() => {
      loadBook()
    })

    return {
      book,
      isInWishlist,
      formatAuthors,
      formatDate,
      handleImageError,
      addToWishlist
    }
  }
}
</script>

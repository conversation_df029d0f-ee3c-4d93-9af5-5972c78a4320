import { defineStore } from 'pinia'
import { ref } from 'vue'
import { 
  fetchAuth<PERSON>, 
  create<PERSON>uthor, 
  updateAuthor, 
  deleteAuthor,
  fetchAuthorById 
} from '../services/adminApi'

export const useAuthorsStore = defineStore('authors', () => {
  const authors = ref([])
  const currentAuthor = ref(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  const fetchAuthorsList = async (params = {}) => {
    try {
      loading.value = true
      const response = await fetchAuthors({
        page: pagination.value.page,
        limit: pagination.value.limit,
        ...params
      })
      
      authors.value = response.authors || response
      pagination.value = {
        page: response.page || 1,
        limit: response.limit || 10,
        total: response.total || authors.value.length,
        totalPages: response.totalPages || Math.ceil((response.total || authors.value.length) / (response.limit || 10))
      }
    } catch (error) {
      console.error('Failed to fetch authors:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchAuthor = async (id) => {
    try {
      loading.value = true
      currentAuthor.value = await fetchAuthorById(id)
      return currentAuthor.value
    } catch (error) {
      console.error('Failed to fetch author:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const saveAuthor = async (authorData) => {
    try {
      loading.value = true
      let result
      
      if (authorData.id) {
        result = await updateAuthor(authorData.id, authorData)
        // Update in local state
        const index = authors.value.findIndex(author => author.id === authorData.id)
        if (index !== -1) {
          authors.value[index] = result
        }
      } else {
        result = await createAuthor(authorData)
        authors.value.unshift(result)
      }
      
      return result
    } catch (error) {
      console.error('Failed to save author:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const removeAuthor = async (id) => {
    try {
      await deleteAuthor(id)
      authors.value = authors.value.filter(author => author.id !== id)
    } catch (error) {
      console.error('Failed to delete author:', error)
      throw error
    }
  }

  const setPage = (page) => {
    pagination.value.page = page
  }

  return {
    authors,
    currentAuthor,
    loading,
    pagination,
    fetchAuthorsList,
    fetchAuthor,
    saveAuthor,
    removeAuthor,
    setPage
  }
})

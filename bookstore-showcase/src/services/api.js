import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000
})

// Request interceptor for loading states
api.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// Mock data for development (replace with real API calls)
const mockBooks = [
  {
    id: '850e8400-e29b-41d4-a716-446655440001',
    title: '<PERSON> and the Philosopher\'s Stone',
    subtitle: 'The Boy Who Lived',
    isbn: '9780747532699',
    description: 'The first book in the Harry Potter series following a young wizard\'s adventures at Hogwarts School of Witchcraft and Wizardry.',
    cover_image_url: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400',
    price: 12.99,
    pages: 223,
    publication_date: '1997-06-26',
    language: 'English',
    format: 'Paperback',
    featured: true,
    in_stock: true,
    stock_quantity: 50,
    rating: 4.8,
    review_count: 1250,
    authors: [{ id: '1', name: 'J.K. Rowling' }],
    categories: [{ id: '1', name: 'Fantasy', slug: 'fantasy' }],
    publisher: { id: '1', name: 'Penguin Random House' }
  },
  {
    id: '850e8400-e29b-41d4-a716-446655440002',
    title: '1984',
    subtitle: 'A Dystopian Social Science Fiction Novel',
    isbn: '9780451524935',
    description: 'A dystopian social science fiction novel exploring themes of totalitarianism, surveillance, and individual freedom.',
    cover_image_url: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400',
    price: 13.99,
    pages: 328,
    publication_date: '1949-06-08',
    language: 'English',
    format: 'Paperback',
    featured: true,
    in_stock: true,
    stock_quantity: 30,
    rating: 4.7,
    review_count: 2100,
    authors: [{ id: '2', name: 'George Orwell' }],
    categories: [{ id: '2', name: 'Science Fiction', slug: 'science-fiction' }],
    publisher: { id: '2', name: 'HarperCollins' }
  },
  {
    id: '850e8400-e29b-41d4-a716-446655440003',
    title: 'Pride and Prejudice',
    subtitle: 'A Classic Romance Novel',
    isbn: '9780141439518',
    description: 'A romantic novel of manners set in Georgian England, following Elizabeth Bennet and Mr. Darcy.',
    cover_image_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    price: 11.99,
    pages: 279,
    publication_date: '1813-01-28',
    language: 'English',
    format: 'Paperback',
    featured: false,
    in_stock: true,
    stock_quantity: 25,
    rating: 4.6,
    review_count: 890,
    authors: [{ id: '3', name: 'Jane Austen' }],
    categories: [{ id: '3', name: 'Romance', slug: 'romance' }],
    publisher: { id: '3', name: 'Simon & Schuster' }
  },
  {
    id: '850e8400-e29b-41d4-a716-446655440004',
    title: 'The Shining',
    subtitle: 'A Horror Masterpiece',
    isbn: '9780307743657',
    description: 'A psychological horror novel about Jack Torrance at the isolated Overlook Hotel.',
    cover_image_url: 'https://images.unsplash.com/photo-1512820790803-83ca734da794?w=400',
    price: 14.99,
    pages: 447,
    publication_date: '1977-01-28',
    language: 'English',
    format: 'Paperback',
    featured: true,
    in_stock: true,
    stock_quantity: 40,
    rating: 4.5,
    review_count: 1560,
    authors: [{ id: '4', name: 'Stephen King' }],
    categories: [{ id: '6', name: 'Horror', slug: 'horror' }],
    publisher: { id: '1', name: 'Penguin Random House' }
  }
]

const mockCategories = [
  { id: '1', name: 'Fiction', slug: 'fiction', description: 'Imaginative literature' },
  { id: '2', name: 'Science Fiction', slug: 'science-fiction', description: 'Speculative fiction' },
  { id: '3', name: 'Romance', slug: 'romance', description: 'Stories of love and relationships' },
  { id: '4', name: 'Mystery', slug: 'mystery', description: 'Puzzles and crime-solving' },
  { id: '5', name: 'Fantasy', slug: 'fantasy', description: 'Magical and supernatural worlds' },
  { id: '6', name: 'Horror', slug: 'horror', description: 'Stories intended to frighten' }
]

export const fetchBooks = async (params = {}) => {
  // Mock implementation - replace with real API call
  await new Promise(resolve => setTimeout(resolve, 500))
  return {
    books: mockBooks,
    total: mockBooks.length,
    page: params.page || 1,
    limit: params.limit || 10
  }
}

export const fetchFeaturedBooks = async () => {
  await new Promise(resolve => setTimeout(resolve, 300))
  return mockBooks.filter(book => book.featured)
}

export const fetchLatestBooks = async () => {
  await new Promise(resolve => setTimeout(resolve, 300))
  return mockBooks.slice(0, 4)
}

export const fetchBookById = async (id) => {
  await new Promise(resolve => setTimeout(resolve, 400))
  return mockBooks.find(book => book.id === id)
}

export const fetchCategories = async () => {
  await new Promise(resolve => setTimeout(resolve, 200))
  return mockCategories
}

export const fetchBooksByCategory = async (categorySlug, params = {}) => {
  await new Promise(resolve => setTimeout(resolve, 400))
  return {
    books: mockBooks,
    category: mockCategories.find(cat => cat.slug === categorySlug),
    total: mockBooks.length,
    page: params.page || 1,
    limit: params.limit || 10
  }
}

export const searchBooks = async (query, params = {}) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  const filteredBooks = mockBooks.filter(book => 
    book.title.toLowerCase().includes(query.toLowerCase()) ||
    book.authors.some(author => author.name.toLowerCase().includes(query.toLowerCase()))
  )
  
  return {
    books: filteredBooks,
    total: filteredBooks.length,
    page: params.page || 1,
    limit: params.limit || 10,
    query
  }
}

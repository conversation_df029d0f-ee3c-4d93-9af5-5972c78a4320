<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">
            {{ isEdit ? 'Edit Author' : 'Add New Author' }}
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEdit ? 'Update author information' : 'Fill in the details to add a new author' }}
          </p>
        </div>
        <router-link
          :to="{ name: 'admin-authors' }"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-2" />
          Back to Authors
        </router-link>
      </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Author Information</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              Full Name *
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter author's full name"
            />
          </div>

          <!-- Birth Date -->
          <div>
            <label for="birth_date" class="block text-sm font-medium text-gray-700 mb-1">
              Birth Date
            </label>
            <input
              id="birth_date"
              v-model="form.birth_date"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <!-- Death Date -->
          <div>
            <label for="death_date" class="block text-sm font-medium text-gray-700 mb-1">
              Death Date
            </label>
            <input
              id="death_date"
              v-model="form.death_date"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <!-- Nationality -->
          <div>
            <label for="nationality" class="block text-sm font-medium text-gray-700 mb-1">
              Nationality
            </label>
            <input
              id="nationality"
              v-model="form.nationality"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter nationality"
            />
          </div>

          <!-- Biography -->
          <div class="lg:col-span-2">
            <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">
              Biography
            </label>
            <textarea
              id="bio"
              v-model="form.bio"
              rows="6"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter author's biography"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <router-link
          :to="{ name: 'admin-authors' }"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </router-link>
        <button
          type="submit"
          :disabled="loading"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ loading ? 'Saving...' : (isEdit ? 'Update Author' : 'Create Author') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { fetchAuthorById, createAuthor, updateAuthor } from '../../../services/adminApi'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

export default {
  name: 'AuthorForm',
  components: {
    ArrowLeftIcon
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()
    
    const loading = ref(false)
    const isEdit = computed(() => !!props.id)
    
    const form = reactive({
      name: '',
      bio: '',
      birth_date: '',
      death_date: '',
      nationality: ''
    })

    const loadAuthor = async () => {
      if (!isEdit.value) return
      
      try {
        loading.value = true
        const author = await fetchAuthorById(props.id)
        
        // Populate form with author data
        Object.keys(form).forEach(key => {
          if (author[key] !== undefined) {
            if ((key === 'birth_date' || key === 'death_date') && author[key]) {
              form[key] = new Date(author[key]).toISOString().split('T')[0]
            } else {
              form[key] = author[key] || ''
            }
          }
        })
      } catch (error) {
        console.error('Failed to load author:', error)
        toast.error('Failed to load author data')
      } finally {
        loading.value = false
      }
    }

    const handleSubmit = async () => {
      try {
        loading.value = true
        
        // Validate required fields
        if (!form.name.trim()) {
          toast.error('Author name is required')
          return
        }
        
        const authorData = { ...form }
        
        if (isEdit.value) {
          await updateAuthor(props.id, authorData)
          toast.success('Author updated successfully')
        } else {
          await createAuthor(authorData)
          toast.success('Author created successfully')
        }
        
        router.push({ name: 'admin-authors' })
      } catch (error) {
        console.error('Failed to save author:', error)
        toast.error(error.response?.data?.message || 'Failed to save author')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadAuthor()
    })

    return {
      loading,
      isEdit,
      form,
      handleSubmit
    }
  }
}
</script>

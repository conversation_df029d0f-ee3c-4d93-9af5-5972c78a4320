<template>
  <section class="relative bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
    <div class="absolute inset-0 bg-black opacity-10"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
        Discover Your Next
        <span class="text-yellow-300 animate-bounce-gentle">Great Read</span>
      </h1>
      <p class="text-xl md:text-2xl mb-8 animate-fade-in opacity-90">
        Explore thousands of books across all genres and find your perfect match
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
        <router-link
          to="/search"
          class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-200 transform hover:scale-105"
        >
          Browse All Books
        </router-link>
        <router-link
          to="/category/featured"
          class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-all duration-200 transform hover:scale-105"
        >
          Featured Collections
        </router-link>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'HeroSection'
}
</script>

import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: '/api/admin',
  timeout: 10000,
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token')
      window.location.href = '/admin/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const adminLogin = async (credentials) => {
  return api.post('/auth/login', credentials)
}

export const adminLogout = async () => {
  return api.post('/auth/logout')
}

export const getAdminProfile = async () => {
  return api.get('/auth/profile')
}

// Dashboard API
export const fetchDashboardStats = async () => {
  return api.get('/dashboard/stats')
}

export const fetchRecentBooks = async () => {
  return api.get('/dashboard/recent-books')
}

export const fetchRecentReviews = async () => {
  return api.get('/dashboard/recent-reviews')
}

// Books API
export const fetchAdminBooks = async (params) => {
  return api.get('/books', { params })
}

export const fetchBookById = async (id) => {
  return api.get(`/books/${id}`)
}

export const createBook = async (bookData) => {
  return api.post('/books', bookData)
}

export const updateBook = async (id, bookData) => {
  return api.put(`/books/${id}`, bookData)
}

export const deleteBook = async (id) => {
  return api.delete(`/books/${id}`)
}

// Authors API
export const fetchAuthors = async (params) => {
  return api.get('/authors', { params })
}

export const fetchAuthorById = async (id) => {
  return api.get(`/authors/${id}`)
}

export const createAuthor = async (authorData) => {
  return api.post('/authors', authorData)
}

export const updateAuthor = async (id, authorData) => {
  return api.put(`/authors/${id}`, authorData)
}

export const deleteAuthor = async (id) => {
  return api.delete(`/authors/${id}`)
}

// Categories API
export const fetchCategories = async (params) => {
  return api.get('/categories', { params })
}

export const fetchCategoryById = async (id) => {
  return api.get(`/categories/${id}`)
}

export const createCategory = async (categoryData) => {
  return api.post('/categories', categoryData)
}

export const updateCategory = async (id, categoryData) => {
  return api.put(`/categories/${id}`, categoryData)
}

export const deleteCategory = async (id) => {
  return api.delete(`/categories/${id}`)
}

// Publishers API
export const fetchPublishers = async (params) => {
  return api.get('/publishers', { params })
}

export const fetchPublisherById = async (id) => {
  return api.get(`/publishers/${id}`)
}

export const createPublisher = async (publisherData) => {
  return api.post('/publishers', publisherData)
}

export const updatePublisher = async (id, publisherData) => {
  return api.put(`/publishers/${id}`, publisherData)
}

export const deletePublisher = async (id) => {
  return api.delete(`/publishers/${id}`)
}

// Reviews API
export const fetchReviews = async (params) => {
  return api.get('/reviews', { params })
}

export const deleteReview = async (id) => {
  return api.delete(`/reviews/${id}`)
}

export const updateReviewStatus = async (id, status) => {
  return api.patch(`/reviews/${id}/status`, { status })
}

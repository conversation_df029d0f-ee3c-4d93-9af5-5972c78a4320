#!/bin/bash

echo "Setting up BookStore Admin Panel..."

# Install dependencies
echo "Installing dependencies..."
npm install

# Copy environment file
if [ ! -f .env ]; then
    echo "Creating .env file from example..."
    cp .env.example .env
    echo "Please edit .env file with your database credentials"
fi

# Create public directory if it doesn't exist
mkdir -p public

echo "Setup complete!"
echo ""
echo "To start the application:"
echo "1. Edit .env file with your database credentials"
echo "2. Run 'npm run server' to start the backend"
echo "3. Run 'npm run dev' to start the frontend"
echo ""
echo "Default admin credentials:"
echo "Email: <EMAIL>"
echo "Password: admin123"

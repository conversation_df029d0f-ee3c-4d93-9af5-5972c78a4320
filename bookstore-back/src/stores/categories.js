import { defineStore } from 'pinia'
import { ref } from 'vue'
import { 
  fetchCategories, 
  createCategory, 
  updateCategory, 
  deleteCategory,
  fetchCategoryById 
} from '../services/adminApi'

export const useCategoriesStore = defineStore('categories', () => {
  const categories = ref([])
  const currentCategory = ref(null)
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0
  })

  const fetchCategoriesList = async (params = {}) => {
    try {
      loading.value = true
      const response = await fetchCategories({
        page: pagination.value.page,
        limit: pagination.value.limit,
        ...params
      })
      
      categories.value = response.categories || response
      pagination.value = {
        page: response.page || 1,
        limit: response.limit || 12,
        total: response.total || categories.value.length,
        totalPages: response.totalPages || Math.ceil((response.total || categories.value.length) / (response.limit || 12))
      }
    } catch (error) {
      console.error('Failed to fetch categories:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const fetchCategory = async (id) => {
    try {
      loading.value = true
      currentCategory.value = await fetchCategoryById(id)
      return currentCategory.value
    } catch (error) {
      console.error('Failed to fetch category:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const saveCategory = async (categoryData) => {
    try {
      loading.value = true
      let result
      
      if (categoryData.id) {
        result = await updateCategory(categoryData.id, categoryData)
        // Update in local state
        const index = categories.value.findIndex(category => category.id === categoryData.id)
        if (index !== -1) {
          categories.value[index] = result
        }
      } else {
        result = await createCategory(categoryData)
        categories.value.unshift(result)
      }
      
      return result
    } catch (error) {
      console.error('Failed to save category:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const removeCategory = async (id) => {
    try {
      await deleteCategory(id)
      categories.value = categories.value.filter(category => category.id !== id)
    } catch (error) {
      console.error('Failed to delete category:', error)
      throw error
    }
  }

  const setPage = (page) => {
    pagination.value.page = page
  }

  return {
    categories,
    currentCategory,
    loading,
    pagination,
    fetchCategoriesList,
    fetchCategory,
    saveCategory,
    removeCategory,
    setPage
  }
})

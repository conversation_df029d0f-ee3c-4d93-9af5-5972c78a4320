<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">
            {{ isEdit ? 'Edit Book' : 'Add New Book' }}
          </h2>
          <p class="mt-1 text-sm text-gray-500">
            {{ isEdit ? 'Update book information' : 'Fill in the details to add a new book' }}
          </p>
        </div>
        <router-link
          :to="{ name: 'admin-books' }"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeftIcon class="h-4 w-4 mr-2" />
          Back to Books
        </router-link>
      </div>
    </div>

    <!-- Form -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Book cover -->
          <div class="lg:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">Book Cover</label>
            <div class="flex items-center space-x-6">
              <div class="shrink-0">
                <img
                  :src="form.cover_image_url || '/placeholder-book.jpg'"
                  alt="Book cover"
                  class="h-32 w-24 object-cover rounded-lg border-2 border-gray-300"
                />
              </div>
              <div class="flex-1">
                <input
                  v-model="form.cover_image_url"
                  type="url"
                  placeholder="Enter image URL"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                />
                <p class="mt-2 text-sm text-gray-500">
                  Enter a URL for the book cover image
                </p>
              </div>
            </div>
          </div>

          <!-- Title -->
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter book title"
            />
          </div>

          <!-- Subtitle -->
          <div>
            <label for="subtitle" class="block text-sm font-medium text-gray-700 mb-1">
              Subtitle
            </label>
            <input
              id="subtitle"
              v-model="form.subtitle"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter book subtitle"
            />
          </div>

          <!-- ISBN -->
          <div>
            <label for="isbn" class="block text-sm font-medium text-gray-700 mb-1">
              ISBN
            </label>
            <input
              id="isbn"
              v-model="form.isbn"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter ISBN"
            />
          </div>

          <!-- Price -->
          <div>
            <label for="price" class="block text-sm font-medium text-gray-700 mb-1">
              Price *
            </label>
            <div class="relative">
              <span class="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">$</span>
              <input
                id="price"
                v-model="form.price"
                type="number"
                step="0.01"
                min="0"
                required
                class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>
          </div>

          <!-- Pages -->
          <div>
            <label for="pages" class="block text-sm font-medium text-gray-700 mb-1">
              Pages
            </label>
            <input
              id="pages"
              v-model="form.pages"
              type="number"
              min="1"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter number of pages"
            />
          </div>

          <!-- Publication Date -->
          <div>
            <label for="publication_date" class="block text-sm font-medium text-gray-700 mb-1">
              Publication Date
            </label>
            <input
              id="publication_date"
              v-model="form.publication_date"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <!-- Language -->
          <div>
            <label for="language" class="block text-sm font-medium text-gray-700 mb-1">
              Language
            </label>
            <select
              id="language"
              v-model="form.language"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Italian">Italian</option>
              <option value="Portuguese">Portuguese</option>
            </select>
          </div>

          <!-- Format -->
          <div>
            <label for="format" class="block text-sm font-medium text-gray-700 mb-1">
              Format
            </label>
            <select
              id="format"
              v-model="form.format"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="Paperback">Paperback</option>
              <option value="Hardcover">Hardcover</option>
              <option value="Ebook">Ebook</option>
              <option value="Audiobook">Audiobook</option>
            </select>
          </div>

          <!-- Stock Quantity -->
          <div>
            <label for="stock_quantity" class="block text-sm font-medium text-gray-700 mb-1">
              Stock Quantity
            </label>
            <input
              id="stock_quantity"
              v-model="form.stock_quantity"
              type="number"
              min="0"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter stock quantity"
            />
          </div>

          <!-- Description -->
          <div class="lg:col-span-2">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter book description"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Authors Section -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Authors</h3>
        <div class="space-y-4">
          <div v-for="(authorId, index) in form.author_ids" :key="index" class="flex items-center space-x-4">
            <div class="flex-1">
              <select
                v-model="form.author_ids[index]"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                required
              >
                <option value="">Select an author</option>
                <option v-for="author in authors" :key="author.id" :value="author.id">
                  {{ author.name }}
                </option>
              </select>
            </div>
            <button
              v-if="form.author_ids.length > 1"
              @click="removeAuthor(index)"
              type="button"
              class="text-red-600 hover:text-red-900"
            >
              <TrashIcon class="h-5 w-5" />
            </button>
          </div>
          <button
            @click="addAuthor"
            type="button"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            Add Author
          </button>
        </div>
      </div>

      <!-- Categories Section -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Categories</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div v-for="category in categories" :key="category.id" class="flex items-center">
            <input
              :id="`category-${category.id}`"
              v-model="form.category_ids"
              :value="category.id"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label :for="`category-${category.id}`" class="ml-2 text-sm text-gray-900">
              {{ category.name }}
            </label>
          </div>
        </div>
      </div>

      <!-- Settings Section -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-6">Settings</h3>
        <div class="space-y-4">
          <div class="flex items-center">
            <input
              id="featured"
              v-model="form.featured"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="featured" class="ml-2 text-sm text-gray-900">
              Featured Book
            </label>
          </div>
          <div class="flex items-center">
            <input
              id="in_stock"
              v-model="form.in_stock"
              type="checkbox"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="in_stock" class="ml-2 text-sm text-gray-900">
              In Stock
            </label>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <router-link
          :to="{ name: 'admin-books' }"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </router-link>
        <button
          type="submit"
          :disabled="loading"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ loading ? 'Saving...' : (isEdit ? 'Update Book' : 'Create Book') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useBooksStore } from '../../../stores/books'
import { fetchAuthors, fetchCategories } from '../../../services/adminApi'
import { ArrowLeftIcon, PlusIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

export default {
  name: 'BookForm',
  components: {
    ArrowLeftIcon,
    PlusIcon,
    TrashIcon
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const booksStore = useBooksStore()
    const toast = useToast()

    const loading = ref(false)
    const authors = ref([])
    const categories = ref([])

    const isEdit = computed(() => !!props.id)

    const form = reactive({
      title: '',
      subtitle: '',
      isbn: '',
      description: '',
      price: '',
      pages: '',
      publication_date: '',
      language: 'English',
      format: 'Paperback',
      cover_image_url: '',
      stock_quantity: 0,
      featured: false,
      in_stock: true,
      author_ids: [''],
      category_ids: []
    })

    const loadFormData = async () => {
      try {
        const [authorsData, categoriesData] = await Promise.all([
          fetchAuthors(),
          fetchCategories()
        ])

        authors.value = authorsData.authors || authorsData
        categories.value = categoriesData.categories || categoriesData

        if (isEdit.value) {
          const book = await booksStore.fetchBook(props.id)

          // Populate form with book data
          Object.keys(form).forEach(key => {
            if (book[key] !== undefined) {
              if (key === 'author_ids') {
                form[key] = book.authors?.map(author => author.id) || ['']
              } else if (key === 'category_ids') {
                form[key] = book.categories?.map(category => category.id) || []
              } else if (key === 'publication_date' && book[key]) {
                form[key] = new Date(book[key]).toISOString().split('T')[0]
              } else {
                form[key] = book[key]
              }
            }
          })

          // Ensure at least one author field
          if (form.author_ids.length === 0) {
            form.author_ids = ['']
          }
        }
      } catch (error) {
        console.error('Failed to load form data:', error)
        toast.error('Failed to load form data')
      }
    }

    const addAuthor = () => {
      form.author_ids.push('')
    }

    const removeAuthor = (index) => {
      form.author_ids.splice(index, 1)
    }

    const handleSubmit = async () => {
      try {
        loading.value = true

        // Validate required fields
        if (!form.title || !form.price) {
          toast.error('Please fill in all required fields')
          return
        }

        // Filter out empty author IDs
        const validAuthorIds = form.author_ids.filter(id => id)
        if (validAuthorIds.length === 0) {
          toast.error('Please select at least one author')
          return
        }

        const bookData = {
          ...form,
          author_ids: validAuthorIds,
          price: parseFloat(form.price),
          pages: form.pages ? parseInt(form.pages) : null,
          stock_quantity: parseInt(form.stock_quantity) || 0
        }

        if (isEdit.value) {
          bookData.id = props.id
        }

        await booksStore.saveBook(bookData)

        toast.success(isEdit.value ? 'Book updated successfully' : 'Book created successfully')
        router.push({ name: 'admin-books' })
      } catch (error) {
        console.error('Failed to save book:', error)
        toast.error(error.response?.data?.message || 'Failed to save book')
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadFormData()
    })

    return {
      loading,
      authors,
      categories,
      isEdit,
      form,
      addAuthor,
      removeAuthor,
      handleSubmit
    }
  }
}
</script>

-- Insert sample data
INSERT INTO publishers (id, name, address, website) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Penguin Random House', 'New York, NY', 'https://penguinrandomhouse.com'),
('550e8400-e29b-41d4-a716-446655440002', 'HarperCollins', 'New York, NY', 'https://harpercollins.com'),
('550e8400-e29b-41d4-a716-446655440003', '<PERSON> & <PERSON>', 'New York, NY', 'https://simonandschuster.com');

INSERT INTO authors (id, name, bio, nationality, image_url) VALUES
('650e8400-e29b-41d4-a716-446655440001', '<PERSON><PERSON><PERSON><PERSON>', 'British author best known for the Harry Potter series.', 'British', 'https://example.com/jk-rowling.jpg'),
('650e8400-e29b-41d4-a716-446655440002', '<PERSON>', 'English novelist and essayist, journalist and critic.', 'British', 'https://example.com/orwell.jpg'),
('650e8400-e29b-41d4-a716-446655440003', 'Jane Austen', 'English novelist known for her wit and social commentary.', 'British', 'https://example.com/austen.jpg'),
('650e8400-e29b-41d4-a716-446655440004', 'Stephen King', 'American author of horror, supernatural fiction, suspense, crime, science-fiction, and fantasy novels.', 'American', 'https://example.com/stephen-king.jpg'),
('650e8400-e29b-41d4-a716-446655440005', 'Agatha Christie', 'English writer known for her detective novels, especially those featuring Hercule Poirot and Miss Marple.', 'British', 'https://example.com/agatha-christie.jpg');

INSERT INTO categories (id, name, description, slug) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'Fiction', 'Imaginative literature including novels and short stories', 'fiction'),
('750e8400-e29b-41d4-a716-446655440002', 'Science Fiction', 'Speculative fiction dealing with futuristic concepts', 'science-fiction'),
('750e8400-e29b-41d4-a716-446655440003', 'Romance', 'Stories focused on romantic relationships', 'romance'),
('750e8400-e29b-41d4-a716-446655440004', 'Mystery', 'Stories involving puzzles and crime-solving', 'mystery'),
('750e8400-e29b-41d4-a716-446655440005', 'Fantasy', 'Stories set in magical or supernatural worlds', 'fantasy'),
('750e8400-e29b-41d4-a716-446655440006', 'Horror', 'Stories intended to frighten, unsettle, or create suspense', 'horror'),
('750e8400-e29b-41d4-a716-446655440007', 'Thriller', 'Fast-paced stories with constant danger', 'thriller');

INSERT INTO books (id, title, subtitle, isbn, description, cover_image_url, price, pages, publication_date, publisher_id, featured, stock_quantity, rating) VALUES
('850e8400-e29b-41d4-a716-446655440001', 'Harry Potter and the Philosopher''s Stone', 'The Boy Who Lived', '9780747532699', 'The first book in the Harry Potter series following a young wizard''s adventures at Hogwarts School of Witchcraft and Wizardry.', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400', 12.99, 223, '1997-06-26', '550e8400-e29b-41d4-a716-446655440001', true, 50, 4.8),
('850e8400-e29b-41d4-a716-446655440002', '1984', 'A Dystopian Social Science Fiction Novel', '9780451524935', 'A dystopian social science fiction novel exploring themes of totalitarianism, surveillance, and individual freedom in a society ruled by Big Brother.', 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=400', 13.99, 328, '1949-06-08', '550e8400-e29b-41d4-a716-446655440002', true, 30, 4.7),
('850e8400-e29b-41d4-a716-446655440003', 'Pride and Prejudice', 'A Classic Romance Novel', '9780141439518', 'A romantic novel of manners set in Georgian England, following the emotional development of Elizabeth Bennet and her complex relationship with Mr. Darcy.', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400', 11.99, 279, '1813-01-28', '550e8400-e29b-41d4-a716-446655440003', false, 25, 4.6),
('850e8400-e29b-41d4-a716-446655440004', 'The Shining', 'A Horror Masterpiece', '9780307743657', 'A psychological horror novel about Jack Torrance, who becomes winter caretaker at the isolated Overlook Hotel in Colorado, hoping to cure his writer''s block.', 'https://images.unsplash.com/photo-1512820790803-83ca734da794?w=400', 14.99, 447, '1977-01-28', '550e8400-e29b-41d4-a716-446655440001', true, 40, 4.5),
('850e8400-e29b-41d4-a716-446655440005', 'Murder on the Orient Express', 'A Hercule Poirot Mystery', '9780062693662', 'Hercule Poirot investigates a murder aboard the famous Orient Express train, where every passenger becomes a suspect in this classic whodunit.', 'https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=400', 12.49, 256, '1934-01-01', '550e8400-e29b-41d4-a716-446655440002', false, 35, 4.4);

-- Link books to authors
INSERT INTO book_authors (book_id, author_id) VALUES
('850e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001'),
('850e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440002'),
('850e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440003'),
('850e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440004'),
('850e8400-e29b-41d4-a716-446655440005', '650e8400-e29b-41d4-a716-446655440005');

-- Link books to categories
INSERT INTO book_categories (book_id, category_id) VALUES
('850e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440005'),
('850e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002'),
('850e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440003'),
('850e8400-e29b-41d4-a716-446655440004', '750e8400-e29b-41d4-a716-446655440006'),
('850e8400-e29b-41d4-a716-446655440005', '750e8400-e29b-41d4-a716-446655440004');
